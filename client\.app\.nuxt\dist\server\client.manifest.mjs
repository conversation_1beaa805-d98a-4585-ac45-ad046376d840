export default {
  "@vite/client": {
    "prefetch": true,
    "isEntry": true,
    "file": "@vite/client",
    "css": [],
    "module": true,
    "resourceType": "script"
  },
  "C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "isEntry": true,
    "file": "C:/Users/<USER>/comanager/client/node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/entry.js"
  }
}
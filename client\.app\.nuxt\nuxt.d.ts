// Generated by nuxi
/// <reference types="@nuxt/devtools" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@nuxtjs/i18n" />
/// <reference types="@nuxt/image" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@vueuse/nuxt" />
/// <reference types="@nuxt/fonts" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@nuxt/content" />
/// <reference types="@vite-pwa/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@pinia/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@nuxtjs/i18n" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="@shuriken-ui/nuxt" />
/// <reference types="reka-ui" />
/// <reference types="pinia-plugin-persistedstate" />
/// <reference types="@pinia/nuxt" />
/// <reference types="reka-ui" />
/// <reference types="reka-ui" />
/// <reference types="reka-ui" />
/// <reference types="pinia-plugin-persistedstate" />
/// <reference types="pinia-plugin-persistedstate" />
/// <reference types="pinia-plugin-persistedstate" />
/// <reference types="pinia-plugin-persistedstate" />
/// <reference types="reka-ui" />
/// <reference types="reka-ui" />
/// <reference types="reka-ui" />
/// <reference types="reka-ui" />
/// <reference types="reka-ui" />
/// <reference types="reka-ui" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference path="component-meta.d.ts" />
/// <reference path="content/types.d.ts" />
/// <reference types="@pinia/nuxt" />
/// <reference path="../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/plugins/types" />
/// <reference types="@vite-pwa/nuxt/configuration" />
/// <reference types="vite-plugin-pwa/vue" />
/// <reference types="vite-plugin-pwa/info" />
/// <reference types="vite-plugin-pwa/pwa-assets" />
/// <reference path="pwa-icons/pwa-icons.d.ts" />
/// <reference path="pwa-icons/PwaTransparentImage.d.ts" />
/// <reference path="pwa-icons/PwaMaskableImage.d.ts" />
/// <reference path="pwa-icons/PwaFaviconImage.d.ts" />
/// <reference path="pwa-icons/PwaAppleImage.d.ts" />
/// <reference path="pwa-icons/PwaAppleSplashScreenImage.d.ts" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="types/view-transitions.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/i18n-plugin.d.ts" />
/// <reference path="types/nitro.d.ts" />

export {}

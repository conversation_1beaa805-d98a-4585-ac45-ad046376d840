// Generated by nitro

// App Config
import type { Defu } from 'defu'

import type { default as appConfig0 } from "../../../layers/core/app.config";
import type { default as appConfig1 } from "../../../layers/budget/app.config";
import type { default as appConfig2 } from "../../../layers/hr/app.config";
import type { default as appConfig3 } from "../../../layers/companies/app.config";
import type { default as appConfig4 } from "../../../layers/production/app.config";
import type { default as appConfig5 } from "../../../layers/accounting/app.config";
import type { default as appConfig6 } from "../../../layers/timemanagement/app.config";

type UserAppConfig = Defu<{}, [typeof appConfig0, typeof appConfig1, typeof appConfig2, typeof appConfig3, typeof appConfig4, typeof appConfig5, typeof appConfig6]>

declare module "nitropack/types" {
  interface AppConfig extends UserAppConfig {}

}
export {}
// Generated by Nuxt'
import type { Plugin } from '#app'

type Decorate<T extends Record<string, any>> = { [K in keyof T as K extends string ? `$${K}` : never]: T[K] }

type InjectionType<A extends Plugin> = A extends {default: Plugin<infer T>} ? Decorate<T> : unknown

type NuxtAppInjections = 
  InjectionType<typeof import("../../../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_5c79cbd05b599f0ffb2fb3def69cce33/node_modules/@pinia/nuxt/dist/runtime/payload-plugin.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/plugins/revive-payload.client.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/head/runtime/plugins/unhead.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/plugins/router.js")> &
  InjectionType<typeof import("../../app/plugins/extract-menu.server")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/plugins/payload.client.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/plugins/navigation-repaint.client.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/plugins/check-outdated-build.client.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/plugins/revive-payload.server.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/plugins/view-transitions.client.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/app/plugins/chunk-reload.client.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@pinia+nuxt@0.11.0_magicast_5c79cbd05b599f0ffb2fb3def69cce33/node_modules/@pinia/nuxt/dist/runtime/plugin.vue3.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/plugins/switch-locale-path-ssr.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/plugins/route-locale-detect.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/plugins/i18n.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/plugins/prefetch.client.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@nuxt+icon@1.13.0_magicast@_3ce591570bb61780297809dbdb2dd187/node_modules/@nuxt/icon/dist/runtime/plugin.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/plugin.server.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@nuxtjs+color-mode@3.5.2_magicast@0.3.5/node_modules/@nuxtjs/color-mode/dist/runtime/plugin.client.js")> &
  InjectionType<typeof import("../pwa-icons-plugin")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/pinia-plugin-persistedstate_a6545990ae5be98b86260c735c5a3f67/node_modules/pinia-plugin-persistedstate/dist/nuxt/runtime/plugin.js")> &
  InjectionType<typeof import("../../../layers/recruitment/plugins/force-light")> &
  InjectionType<typeof import("../../../layers/core/plugins/dashboard-components")> &
  InjectionType<typeof import("../../../layers/tairo/plugins/directives")> &
  InjectionType<typeof import("../../app/plugins/apexcharts.client")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/nuxt@3.16.2_@parcel+watcher_d4380d955f8be69313b004a86c9999b3/node_modules/nuxt/dist/pages/runtime/plugins/prerender.server.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@nuxtjs+i18n@9.5.5_@vue+com_3284b88729ef0726562cce13c47ff6ab/node_modules/@nuxtjs/i18n/dist/runtime/plugins/ssg-detect.js")> &
  InjectionType<typeof import("../../../node_modules/.pnpm/@vite-pwa+nuxt@1.0.3_magica_4b7c4ddd815a6547148f784feb34ea4d/node_modules/@vite-pwa/nuxt/dist/runtime/plugins/pwa.client.js")> &
  InjectionType<typeof import("../../app/plugins/theme-color.client")>

declare module '#app' {
  interface NuxtApp extends NuxtAppInjections { }

  interface NuxtAppLiterals {
    pluginName: 'nuxt:revive-payload:client' | 'nuxt:head' | 'nuxt:router' | 'extract-menu' | 'nuxt:payload' | 'nuxt:revive-payload:server' | 'nuxt:chunk-reload' | 'pinia' | 'i18n:plugin:switch-locale-path-ssr' | 'i18n:plugin:route-locale-detect' | 'i18n:plugin' | 'nuxt:global-components' | 'nuxt:prefetch' | '@nuxt/icon' | 'vite-pwa:nuxt:pwa-icons-plugin' | 'pinia-plugin-persistedstate' | 'i18n:plugin:ssg-detect' | 'vite-pwa:nuxt:client:plugin' | 'theme-color-plugin'
  }
}

declare module 'vue' {
  interface ComponentCustomProperties extends NuxtAppInjections { }
}

export { }


// @ts-nocheck


export const localeCodes =  [
  "en",
  "fr",
  "es",
  "de",
  "ar",
  "ja"
]

export const localeLoaders = {
  en: [
    {
      key: "locale_en_45US_46yaml_5cb166de",
      load: () => import("#nuxt-i18n/5cb166de" /* webpackChunkName: "locale_en_45US_46yaml_5cb166de" */),
      cache: true
    }
  ],
  fr: [
    {
      key: "locale_fr_45FR_46yaml_580bae4d",
      load: () => import("#nuxt-i18n/580bae4d" /* webpackChunkName: "locale_fr_45FR_46yaml_580bae4d" */),
      cache: true
    }
  ],
  es: [
    {
      key: "locale_es_45ES_46yaml_3d831575",
      load: () => import("#nuxt-i18n/3d831575" /* webpackChunkName: "locale_es_45ES_46yaml_3d831575" */),
      cache: true
    }
  ],
  de: [
    {
      key: "locale_de_45DE_46yaml_ac1b52e7",
      load: () => import("#nuxt-i18n/ac1b52e7" /* webpackChunkName: "locale_de_45DE_46yaml_ac1b52e7" */),
      cache: true
    }
  ],
  ar: [
    {
      key: "locale_ar_45SA_46yaml_8692af80",
      load: () => import("#nuxt-i18n/8692af80" /* webpackChunkName: "locale_ar_45SA_46yaml_8692af80" */),
      cache: true
    }
  ],
  ja: [
    {
      key: "locale_ja_45JP_46yaml_5f9610bb",
      load: () => import("#nuxt-i18n/5f9610bb" /* webpackChunkName: "locale_ja_45JP_46yaml_5f9610bb" */),
      cache: true
    }
  ]
}

export const vueI18nConfigs = []

export const nuxtI18nOptions = {
  restructureDir: "i18n",
  experimental: {
    localeDetector: "",
    switchLocalePathLinkSSR: false,
    autoImportTranslationFunctions: false,
    typedPages: true,
    typedOptionsAndMessages: false,
    generatedLocaleFilePathFormat: "off",
    alternateLinkCanonicalQueries: false,
    hmr: true
  },
  bundle: {
    compositionOnly: true,
    runtimeOnly: false,
    fullInstall: true,
    dropMessageCompiler: false,
    optimizeTranslationDirective: false
  },
  compilation: {
    strictMessage: true,
    escapeHtml: false
  },
  customBlocks: {
    defaultSFCLang: "json",
    globalSFCScope: false
  },
  locales: [
    {
      code: "en",
      dir: "ltr",
      language: "en-US",
      name: "English",
      isCatchallLocale: true
    },
    {
      code: "fr",
      dir: "ltr",
      language: "fr-FR",
      name: "Français"
    },
    {
      code: "es",
      dir: "ltr",
      language: "es-ES",
      name: "Español"
    },
    {
      code: "de",
      dir: "ltr",
      language: "de-DE",
      name: "Deutsch"
    },
    {
      code: "ar",
      dir: "rtl",
      language: "ar-SA",
      name: "العربية"
    },
    {
      code: "ja",
      dir: "ltr",
      language: "ja-JP",
      name: "日本語"
    }
  ],
  defaultLocale: "en",
  defaultDirection: "ltr",
  routesNameSeparator: "___",
  trailingSlash: false,
  defaultLocaleRouteNameSuffix: "default",
  strategy: "no_prefix",
  lazy: true,
  langDir: "locales",
  rootRedirect: undefined,
  detectBrowserLanguage: {
    alwaysRedirect: false,
    cookieCrossOrigin: false,
    cookieDomain: null,
    cookieKey: "i18n_redirected",
    cookieSecure: false,
    fallbackLocale: "",
    redirectOn: "root",
    useCookie: true
  },
  differentDomains: false,
  baseUrl: "/",
  customRoutes: "page",
  pages: {},
  skipSettingLocaleOnNavigate: false,
  types: "composition",
  debug: false,
  parallelPlugin: false,
  multiDomainLocales: false,
  i18nModules: []
}

export const normalizedLocales = [
  {
    code: "en",
    dir: "ltr",
    language: "en-US",
    name: "English",
    isCatchallLocale: true
  },
  {
    code: "fr",
    dir: "ltr",
    language: "fr-FR",
    name: "Français"
  },
  {
    code: "es",
    dir: "ltr",
    language: "es-ES",
    name: "Español"
  },
  {
    code: "de",
    dir: "ltr",
    language: "de-DE",
    name: "Deutsch"
  },
  {
    code: "ar",
    dir: "rtl",
    language: "ar-SA",
    name: "العربية"
  },
  {
    code: "ja",
    dir: "ltr",
    language: "ja-JP",
    name: "日本語"
  }
]

export const NUXT_I18N_MODULE_ID = "@nuxtjs/i18n"
export const parallelPlugin = false
export const isSSG = false
export const hasPages = true

export const DEFAULT_COOKIE_KEY = "i18n_redirected"
export const DEFAULT_DYNAMIC_PARAMS_KEY = "nuxtI18nInternal"
export const SWITCH_LOCALE_PATH_LINK_IDENTIFIER = "nuxt-i18n-slp"
/** client **/
if(import.meta.hot) {

function deepEqual(a, b, ignoreKeys = []) {
  // Same reference?
  if (a === b) return true

  // Check if either is null or not an object
  if (a == null || b == null || typeof a !== 'object' || typeof b !== 'object') {
    return false
  }

  // Get top-level keys, excluding ignoreKeys
  const keysA = Object.keys(a).filter(k => !ignoreKeys.includes(k))
  const keysB = Object.keys(b).filter(k => !ignoreKeys.includes(k))

  // Must have the same number of keys (after ignoring)
  if (keysA.length !== keysB.length) {
    return false
  }

  // Check each property
  for (const key of keysA) {
    if (!keysB.includes(key)) {
      return false
    }

    const valA = a[key]
    const valB = b[key]

    // Compare functions stringified
    if (typeof valA === 'function' && typeof valB === 'function') {
      if (valA.toString() !== valB.toString()) {
        return false
      }
    }
    // If nested, do a normal recursive check (no ignoring at deeper levels)
    else if (typeof valA === 'object' && typeof valB === 'object') {
      if (!deepEqual(valA, valB)) {
        return false
      }
    }
    // Compare primitive values
    else if (valA !== valB) {
      return false
    }
  }

  return true
}



async function loadCfg(config) {
  const nuxt = useNuxtApp()
  const { default: resolver } = await config()
  return typeof resolver === 'function' ? await nuxt.runWithContext(() => resolver()) : resolver
}


  import.meta.hot.accept("../i18n/locales/en-US.yaml", async mod => {
    localeLoaders["en"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("en")
  })

  import.meta.hot.accept("../i18n/locales/fr-FR.yaml", async mod => {
    localeLoaders["fr"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("fr")
  })

  import.meta.hot.accept("../i18n/locales/es-ES.yaml", async mod => {
    localeLoaders["es"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("es")
  })

  import.meta.hot.accept("../i18n/locales/de-DE.yaml", async mod => {
    localeLoaders["de"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("de")
  })

  import.meta.hot.accept("../i18n/locales/ar-SA.yaml", async mod => {
    localeLoaders["ar"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("ar")
  })

  import.meta.hot.accept("../i18n/locales/ja-JP.yaml", async mod => {
    localeLoaders["ja"][0].load = () => Promise.resolve(mod.default)
    await useNuxtApp()._nuxtI18nDev.resetI18nProperties("ja")
  })



}
/** client-end **/
function createRemoteCollection(fetchEndpoint) {
  let _cache
  return async () => {
    if (_cache)
      return _cache
    const res = await fetch(fetchEndpoint).then(r => r.json())
    _cache = res
    return res
  }
}

export const collections = {
  'akar-icons': () => import('@iconify-json/akar-icons/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'bi': () => import('@iconify-json/bi/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'cib': () => import('@iconify-json/cib/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'cryptocurrency': () => import('@iconify-json/cryptocurrency/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'devicon': () => import('@iconify-json/devicon/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'fa-brands': () => import('@iconify-json/fa-brands/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'fa6-brands': () => import('@iconify-json/fa6-brands/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'feather': () => import('@iconify-json/feather/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'file-icons': () => import('@iconify-json/file-icons/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'flagpack': () => import('@iconify-json/flagpack/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'gg': () => import('@iconify-json/gg/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'guidance': () => import('@iconify-json/guidance/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'ic': () => import('@iconify-json/ic/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'icon-park-outline': () => import('@iconify-json/icon-park-outline/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'ion': () => import('@iconify-json/ion/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'logos': () => import('@iconify-json/logos/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'lucide': () => import('@iconify-json/lucide/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'mdi': () => import('@iconify-json/mdi/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'mdi-light': () => import('@iconify-json/mdi-light/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'mingcute': () => import('@iconify-json/mingcute/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'nonicons': () => import('@iconify-json/nonicons/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'pajamas': () => import('@iconify-json/pajamas/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'ph': () => import('@iconify-json/ph/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'ri': () => import('@iconify-json/ri/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'simple-icons': () => import('@iconify-json/simple-icons/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'solar': () => import('@iconify-json/solar/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'streamline': () => import('@iconify-json/streamline/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'system-uicons': () => import('@iconify-json/system-uicons/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'teenyicons': () => import('@iconify-json/teenyicons/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'token-branded': () => import('@iconify-json/token-branded/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'uiw': () => import('@iconify-json/uiw/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'unjs': () => import('@iconify-json/unjs/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'vaadin': () => import('@iconify-json/vaadin/icons.json', { with: { type: 'json' } }).then(m => m.default),
  'nui-icon': () => ({"prefix":"nui-icon","icons":{"moon":{"width":24,"height":24,"body":"<path\n    fill=\"currentColor\"\n    stroke=\"currentColor\"\n    d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"\n  />"},"rekaui-icon":{"width":290,"height":290,"body":"<defs><linearGradient id=\"paint0_linear_30_25\" x1=\"241.666\" y1=\"264.867\" x2=\"88.9331\" y2=\"7.73334\" gradientUnits=\"userSpaceOnUse\">\n<stop stop-color=\"#00996C\"/>\n<stop offset=\"1\" stop-color=\"#7AFFD8\"/>\n</linearGradient>\n<linearGradient id=\"paint1_linear_30_25\" x1=\"192.366\" y1=\"289.033\" x2=\"233.933\" y2=\"42.5333\" gradientUnits=\"userSpaceOnUse\">\n<stop stop-color=\"white\" stop-opacity=\"0.56\"/>\n<stop offset=\"0.494792\" stop-color=\"white\" stop-opacity=\"0\"/>\n<stop offset=\"1\" stop-color=\"white\"/>\n</linearGradient>\n<radialGradient id=\"paint2_radial_30_25\" cx=\"0\" cy=\"0\" r=\"1\" gradientUnits=\"userSpaceOnUse\" gradientTransform=\"translate(185.6 110.2) rotate(147.858) scale(129.006 128.619)\">\n<stop offset=\"0.48614\" stop-color=\"#00C58A\"/>\n<stop offset=\"1\" stop-color=\"white\" stop-opacity=\"0.21\"/>\n</radialGradient>\n<radialGradient id=\"paint3_radial_30_25\" cx=\"0\" cy=\"0\" r=\"1\" gradientUnits=\"userSpaceOnUse\" gradientTransform=\"translate(108.266 160.467) rotate(90) scale(191.4 190.827)\">\n<stop stop-color=\"white\" stop-opacity=\"0\"/>\n<stop offset=\"1\" stop-color=\"white\"/>\n</radialGradient>\n<radialGradient id=\"paint4_radial_30_25\" cx=\"0\" cy=\"0\" r=\"1\" gradientUnits=\"userSpaceOnUse\" gradientTransform=\"translate(108.266 181.733) rotate(-90) scale(129.533 129.145)\">\n<stop stop-color=\"white\" stop-opacity=\"0\"/>\n<stop offset=\"1\" stop-color=\"white\"/>\n</radialGradient>\n<linearGradient id=\"paint5_linear_30_25\" x1=\"105.366\" y1=\"105.367\" x2=\"105.366\" y2=\"261\" gradientUnits=\"userSpaceOnUse\">\n<stop stop-color=\"white\" stop-opacity=\"0.6\"/>\n<stop offset=\"0.494792\" stop-color=\"white\" stop-opacity=\"0\"/>\n<stop offset=\"1\" stop-color=\"white\" stop-opacity=\"0.38\"/>\n</linearGradient></defs><g fill=\"none\"><path d=\"M226.269 52.4044L226.274 52.4067C237.406 58.2619 245.614 66.3008 250.94 76.5218C256.285 86.7776 258.969 98.4614 258.969 111.596C258.969 124.732 256.285 136.34 250.943 146.446C245.618 156.521 237.447 164.451 226.389 170.234C221.59 172.712 219.355 178.881 222.459 183.542L257.922 236.789C261.773 242.571 257.628 250.311 250.681 250.311H196.906C193.806 250.311 190.939 248.661 189.382 245.98L79.3991 56.5686C76.0313 50.7687 80.2159 43.5 86.9227 43.5H183.394C200.888 43.5 215.161 46.4896 226.269 52.4044Z\" fill=\"url(#paint0_linear_30_25)\" stroke=\"url(#paint1_linear_30_25)\" stroke-width=\"1.93333\"/>\n<path d=\"M116.722 247.228C113.004 253.687 103.684 253.687 99.9661 247.228L26.2042 119.085C22.4947 112.64 27.1462 104.596 34.5821 104.596L182.106 104.596C189.542 104.596 194.193 112.64 190.484 119.085L116.722 247.228Z\" fill=\"url(#paint2_radial_30_25)\"/>\n<path d=\"M116.722 247.228C113.004 253.687 103.684 253.687 99.9661 247.228L26.2042 119.085C22.4947 112.64 27.1462 104.596 34.5821 104.596L182.106 104.596C189.542 104.596 194.193 112.64 190.484 119.085L116.722 247.228Z\" fill=\"url(#paint3_radial_30_25)\" fill-opacity=\"0.5\"/>\n<path d=\"M100.804 246.745L27.042 118.602C23.7034 112.802 27.8898 105.562 34.5821 105.562L182.106 105.562C188.798 105.562 192.985 112.802 189.646 118.602L115.884 246.745C112.538 252.558 104.15 252.558 100.804 246.745Z\" fill=\"url(#paint4_radial_30_25)\" fill-opacity=\"0.5\" stroke=\"url(#paint5_linear_30_25)\" stroke-width=\"1.93333\" stroke-linejoin=\"round\"/></g>"},"screen-duotone":{"width":24,"height":24,"body":"<path fill=\"currentColor\" opacity=\"0.4\" d=\"M19 4H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2\"/>\n  <path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 16h7a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2zm0 0v4m0 0h4m-4 0H8\"/>"},"screen-fill":{"width":24,"height":24,"body":"<path fill=\"currentColor\" d=\"M19 4H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2\"/>\n  <path fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 16h7a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2zm0 0v4m0 0h4m-4 0H8\"/>"},"screen":{"width":24,"height":24,"body":"<path stroke=\"currentColor\" fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 16h7a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2zm0 0v4m0 0h4m-4 0H8\"/>"},"shurikenui-icon":{"width":169.8,"height":169.8,"body":"<defs>\n    <clipPath id=\"clippath\">\n      <path fill=\"currentColor\" d=\"M114.6,11.9l-12.3-7.1c-11.1-6.4-23.8-6.4-34.8,0l-21.6,12.5-21.6,12.5c-11.1,6.4-17.5,17.3-17.5,30.1v49.9c0,12.8,6.3,23.8,17.4,30.2l21.6,12.5,21.7,12.5c11.1,6.4,23.8,6.4,34.8,0l21.6-12.5,9.3-5.4,12.3-7.1c11.1-6.4,17.4-17.4,17.4-30.2v-49.9c0-12.8-6.3-23.8-17.4-30.2l-21.6-12.5-9.3-5.3ZM84.9,64.3c11.4,0,20.6,9.2,20.6,20.6s-9.2,20.6-20.6,20.6-20.6-9.2-20.6-20.6,9.2-20.6,20.6-20.6ZM36.5,112.8l-29.7-17.1,29.7-.4v-10.8c0-17.4,9.5-32.7,23.4-41.1l25-14.4,29.7-17.1-14.5,25.9,9.8,5.7c14,8.5,23.4,23.9,23.4,41.4v62.2l-15.2-25.6-9.9,5.7c-6.9,3.8-14.9,6-23.3,6s-16.4-2.2-23.3-6l-25.1-14.4Z\"/>\n    </clipPath>\n  </defs>\n  <rect clip-path=\"url(#clippath)\" x=\"6.8\" y=\"-1.6\" width=\"156.2\" height=\"173\"/>"},"spiner-bold":{"width":24,"height":24,"body":"<circle\n    cx=\"12\"\n    cy=\"12\"\n    r=\"10\"\n    fill=\"none\"\n    stroke-width=\"3\"\n    stroke=\"currentColor\"\n    stroke-linecap=\"round\"\n    stroke-dasharray=\"1,75\"\n    stroke-dashoffset=\"0\"\n  >\n    <animate\n      attributeName=\"stroke-dasharray\"\n      values=\"1,75;45,75;45,75\"\n      dur=\"1.5s\"\n      repeatCount=\"indefinite\"\n    />\n    <animate\n      attributeName=\"stroke-dashoffset\"\n      values=\"0;-30;-70\"\n      dur=\"1.5s\"\n      repeatCount=\"indefinite\"\n    />\n    <animateTransform\n      attributeName=\"transform\"\n      type=\"rotate\"\n      from=\"0 12 12\"\n      to=\"360 12 12\"\n      dur=\"2s\"\n      repeatCount=\"indefinite\"\n    />\n  </circle>"},"spiner-thin":{"width":24,"height":24,"body":"<circle\n    cx=\"12\"\n    cy=\"12\"\n    r=\"11\"\n    fill=\"none\"\n    stroke-width=\"0.5\"\n    stroke=\"currentColor\"\n    stroke-linecap=\"round\"\n    stroke-dasharray=\"1,75\"\n    stroke-dashoffset=\"0\"\n  >\n    <animate\n      attributeName=\"stroke-dasharray\"\n      values=\"1,75;45,75;45,75\"\n      dur=\"1.5s\"\n      repeatCount=\"indefinite\"\n    />\n    <animate\n      attributeName=\"stroke-dashoffset\"\n      values=\"0;-30;-70\"\n      dur=\"1.5s\"\n      repeatCount=\"indefinite\"\n    />\n    <animateTransform\n      attributeName=\"transform\"\n      type=\"rotate\"\n      from=\"0 12 12\"\n      to=\"360 12 12\"\n      dur=\"2s\"\n      repeatCount=\"indefinite\"\n    />\n  </circle>"},"spiner":{"width":24,"height":24,"body":"<circle\n    cx=\"12\"\n    cy=\"12\"\n    r=\"11\"\n    fill=\"none\"\n    stroke-width=\"2\"\n    stroke=\"currentColor\"\n    stroke-linecap=\"round\"\n    stroke-dasharray=\"1,75\"\n    stroke-dashoffset=\"0\"\n  >\n    <animate\n      attributeName=\"stroke-dasharray\"\n      values=\"1,75;45,75;45,75\"\n      dur=\"1.5s\"\n      repeatCount=\"indefinite\"\n    />\n    <animate\n      attributeName=\"stroke-dashoffset\"\n      values=\"0;-30;-70\"\n      dur=\"1.5s\"\n      repeatCount=\"indefinite\"\n    />\n    <animateTransform\n      attributeName=\"transform\"\n      type=\"rotate\"\n      from=\"0 12 12\"\n      to=\"360 12 12\"\n      dur=\"2s\"\n      repeatCount=\"indefinite\"\n    />\n  </circle>"},"sun":{"width":24,"height":24,"body":"<circle fill=\"currentColor\" stroke=\"currentColor\" cx=\"12\" cy=\"12\" r=\"5\" />\n  <path\n    fill=\"currentColor\"\n    stroke=\"currentColor\"\n    d=\"M12 1v2m0 18v2M4.22 4.22l1.42 1.42m12.72 12.72 1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"\n  />"}}}),
}
import {
  assign,
  create,
  createEmitter,
  deepCopy,
  escapeHtml,
  format,
  friendlyJSONstringify,
  generateCodeFrame,
  generateFormatCache<PERSON>ey,
  getGlobalThis,
  hasOwn,
  inBrowser,
  isArray,
  isBoolean,
  isDate,
  isEmptyObject,
  isFunction,
  isNumber,
  isObject,
  isPlainObject,
  isPromise,
  isRegExp,
  isString,
  isSymbol,
  join,
  makeSymbol,
  mark,
  measure,
  objectToString,
  toDisplayString,
  toTypeString,
  warn,
  warnOnce
} from "./chunk-2MEU474C.js";
import "./chunk-DI52DQAC.js";
export {
  assign,
  create,
  createEmitter,
  deepCopy,
  escapeHtml,
  format,
  friendlyJSONstringify,
  generateCodeFrame,
  generateFormatCacheKey,
  getGlobalThis,
  hasOwn,
  inBrowser,
  isArray,
  isBoolean,
  isDate,
  isEmptyObject,
  isFunction,
  isNumber,
  isObject,
  isPlainObject,
  isPromise,
  isRegExp,
  isString,
  isSymbol,
  join,
  makeSymbol,
  mark,
  measure,
  objectToString,
  toDisplayString,
  toTypeString,
  warn,
  warnOnce
};

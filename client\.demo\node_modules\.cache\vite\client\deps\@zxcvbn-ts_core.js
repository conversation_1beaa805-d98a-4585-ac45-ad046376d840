import "./chunk-DI52DQAC.js";

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/helper.esm.js
var extend = (listToExtend, list) => (
  // eslint-disable-next-line prefer-spread
  listToExtend.push.apply(listToExtend, list)
);
var sorted = (matches) => matches.sort((m1, m2) => m1.i - m2.i || m1.j - m2.j);
var buildRankedDictionary = (orderedList) => {
  const result = {};
  let counter = 1;
  orderedList.forEach((word) => {
    result[word] = counter;
    counter += 1;
  });
  return result;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/data/dateSplits.esm.js
var dateSplits = {
  4: [
    // for length-4 strings, eg 1191 or 9111, two ways to split:
    [1, 2],
    [2, 3]
    // 91 1 1
  ],
  5: [
    [1, 3],
    [2, 3],
    //  [2, 3], // 91 1 11    <- duplicate previous one
    [2, 4]
    // 91 11 1    <- New and must be added as bug fix
  ],
  6: [
    [1, 2],
    [2, 4],
    [4, 5]
    // 1991 1 1
  ],
  //  1111991
  7: [
    [1, 3],
    [2, 3],
    [4, 5],
    [4, 6]
    // 1991 11 1
  ],
  8: [
    [2, 4],
    [4, 6]
    // 1991 11 11
  ]
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/data/const.esm.js
var DATE_MAX_YEAR = 2050;
var DATE_MIN_YEAR = 1e3;
var DATE_SPLITS = dateSplits;
var BRUTEFORCE_CARDINALITY = 10;
var MIN_GUESSES_BEFORE_GROWING_SEQUENCE = 1e4;
var MIN_SUBMATCH_GUESSES_SINGLE_CHAR = 10;
var MIN_SUBMATCH_GUESSES_MULTI_CHAR = 50;
var MIN_YEAR_SPACE = 20;
var START_UPPER = /^[A-Z\xbf-\xdf][^A-Z\xbf-\xdf]+$/;
var END_UPPER = /^[^A-Z\xbf-\xdf]+[A-Z\xbf-\xdf]$/;
var ALL_UPPER = /^[A-Z\xbf-\xdf]+$/;
var ALL_UPPER_INVERTED = /^[^a-z\xdf-\xff]+$/;
var ALL_LOWER = /^[a-z\xdf-\xff]+$/;
var ALL_LOWER_INVERTED = /^[^A-Z\xbf-\xdf]+$/;
var ONE_LOWER = /[a-z\xdf-\xff]/;
var ONE_UPPER = /[A-Z\xbf-\xdf]/;
var ALPHA_INVERTED = /[^A-Za-z\xbf-\xdf]/gi;
var ALL_DIGIT = /^\d+$/;
var REFERENCE_YEAR = (/* @__PURE__ */ new Date()).getFullYear();
var REGEXEN = {
  recentYear: /19\d\d|200\d|201\d|202\d/g
};
var SEPERATOR_CHARS = [" ", ",", ";", ":", "|", "/", "\\", "_", ".", "-"];
var SEPERATOR_CHAR_COUNT = SEPERATOR_CHARS.length;

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/date/matching.esm.js
var MatchDate = class {
  /*
   * a "date" is recognized as:
   *   any 3-tuple that starts or ends with a 2- or 4-digit year,
   *   with 2 or 0 separator chars (1.1.91 or 1191),
   *   maybe zero-padded (01-01-91 vs 1-1-91),
   *   a month between 1 and 12,
   *   a day between 1 and 31.
   *
   * note: this isn't true date parsing in that "feb 31st" is allowed,
   * this doesn't check for leap years, etc.
   *
   * recipe:
   * start with regex to find maybe-dates, then attempt to map the integers
   * onto month-day-year to filter the maybe-dates into dates.
   * finally, remove matches that are substrings of other matches to reduce noise.
   *
   * note: instead of using a lazy or greedy regex to find many dates over the full string,
   * this uses a ^...$ regex against every substring of the password -- less performant but leads
   * to every possible date match.
   */
  match({
    password
  }) {
    const matches = [...this.getMatchesWithoutSeparator(password), ...this.getMatchesWithSeparator(password)];
    const filteredMatches = this.filterNoise(matches);
    return sorted(filteredMatches);
  }
  getMatchesWithSeparator(password) {
    const matches = [];
    const maybeDateWithSeparator = /^(\d{1,4})([\s/\\_.-])(\d{1,2})\2(\d{1,4})$/;
    for (let i = 0; i <= Math.abs(password.length - 6); i += 1) {
      for (let j = i + 5; j <= i + 9; j += 1) {
        if (j >= password.length) {
          break;
        }
        const token = password.slice(i, +j + 1 || 9e9);
        const regexMatch = maybeDateWithSeparator.exec(token);
        if (regexMatch != null) {
          const dmy = this.mapIntegersToDayMonthYear([parseInt(regexMatch[1], 10), parseInt(regexMatch[3], 10), parseInt(regexMatch[4], 10)]);
          if (dmy != null) {
            matches.push({
              pattern: "date",
              token,
              i,
              j,
              separator: regexMatch[2],
              year: dmy.year,
              month: dmy.month,
              day: dmy.day
            });
          }
        }
      }
    }
    return matches;
  }
  // eslint-disable-next-line max-statements
  getMatchesWithoutSeparator(password) {
    const matches = [];
    const maybeDateNoSeparator = /^\d{4,8}$/;
    const metric = (candidate) => Math.abs(candidate.year - REFERENCE_YEAR);
    for (let i = 0; i <= Math.abs(password.length - 4); i += 1) {
      for (let j = i + 3; j <= i + 7; j += 1) {
        if (j >= password.length) {
          break;
        }
        const token = password.slice(i, +j + 1 || 9e9);
        if (maybeDateNoSeparator.exec(token)) {
          const candidates = [];
          const index = token.length;
          const splittedDates = DATE_SPLITS[index];
          splittedDates.forEach(([k, l]) => {
            const dmy = this.mapIntegersToDayMonthYear([parseInt(token.slice(0, k), 10), parseInt(token.slice(k, l), 10), parseInt(token.slice(l), 10)]);
            if (dmy != null) {
              candidates.push(dmy);
            }
          });
          if (candidates.length > 0) {
            let bestCandidate = candidates[0];
            let minDistance = metric(candidates[0]);
            candidates.slice(1).forEach((candidate) => {
              const distance2 = metric(candidate);
              if (distance2 < minDistance) {
                bestCandidate = candidate;
                minDistance = distance2;
              }
            });
            matches.push({
              pattern: "date",
              token,
              i,
              j,
              separator: "",
              year: bestCandidate.year,
              month: bestCandidate.month,
              day: bestCandidate.day
            });
          }
        }
      }
    }
    return matches;
  }
  /*
   * matches now contains all valid date strings in a way that is tricky to capture
   * with regexes only. while thorough, it will contain some unintuitive noise:
   *
   * '2015_06_04', in addition to matching 2015_06_04, will also contain
   * 5(!) other date matches: 15_06_04, 5_06_04, ..., even 2015 (matched as 5/1/2020)
   *
   * to reduce noise, remove date matches that are strict substrings of others
   */
  filterNoise(matches) {
    return matches.filter((match) => {
      let isSubmatch = false;
      const matchesLength = matches.length;
      for (let o = 0; o < matchesLength; o += 1) {
        const otherMatch = matches[o];
        if (match !== otherMatch) {
          if (otherMatch.i <= match.i && otherMatch.j >= match.j) {
            isSubmatch = true;
            break;
          }
        }
      }
      return !isSubmatch;
    });
  }
  /*
   * given a 3-tuple, discard if:
   *   middle int is over 31 (for all dmy formats, years are never allowed in the middle)
   *   middle int is zero
   *   any int is over the max allowable year
   *   any int is over two digits but under the min allowable year
   *   2 integers are over 31, the max allowable day
   *   2 integers are zero
   *   all integers are over 12, the max allowable month
   */
  // eslint-disable-next-line complexity, max-statements
  mapIntegersToDayMonthYear(integers) {
    if (integers[1] > 31 || integers[1] <= 0) {
      return null;
    }
    let over12 = 0;
    let over31 = 0;
    let under1 = 0;
    for (let o = 0, len1 = integers.length; o < len1; o += 1) {
      const int = integers[o];
      if (int > 99 && int < DATE_MIN_YEAR || int > DATE_MAX_YEAR) {
        return null;
      }
      if (int > 31) {
        over31 += 1;
      }
      if (int > 12) {
        over12 += 1;
      }
      if (int <= 0) {
        under1 += 1;
      }
    }
    if (over31 >= 2 || over12 === 3 || under1 >= 2) {
      return null;
    }
    return this.getDayMonth(integers);
  }
  // eslint-disable-next-line max-statements
  getDayMonth(integers) {
    const possibleYearSplits = [
      [integers[2], integers.slice(0, 2)],
      [integers[0], integers.slice(1, 3)]
      // year first
    ];
    const possibleYearSplitsLength = possibleYearSplits.length;
    for (let j = 0; j < possibleYearSplitsLength; j += 1) {
      const [y, rest] = possibleYearSplits[j];
      if (DATE_MIN_YEAR <= y && y <= DATE_MAX_YEAR) {
        const dm = this.mapIntegersToDayMonth(rest);
        if (dm != null) {
          return {
            year: y,
            month: dm.month,
            day: dm.day
          };
        }
        return null;
      }
    }
    for (let k = 0; k < possibleYearSplitsLength; k += 1) {
      const [y, rest] = possibleYearSplits[k];
      const dm = this.mapIntegersToDayMonth(rest);
      if (dm != null) {
        return {
          year: this.twoToFourDigitYear(y),
          month: dm.month,
          day: dm.day
        };
      }
    }
    return null;
  }
  mapIntegersToDayMonth(integers) {
    const temp = [integers, integers.slice().reverse()];
    for (let i = 0; i < temp.length; i += 1) {
      const data = temp[i];
      const day = data[0];
      const month = data[1];
      if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {
        return {
          day,
          month
        };
      }
    }
    return null;
  }
  twoToFourDigitYear(year) {
    if (year > 99) {
      return year;
    }
    if (year > 50) {
      return year + 1900;
    }
    return year + 2e3;
  }
};

// ../node_modules/.pnpm/fastest-levenshtein@1.0.16/node_modules/fastest-levenshtein/esm/mod.js
var peq = new Uint32Array(65536);
var myers_32 = (a, b) => {
  const n = a.length;
  const m = b.length;
  const lst = 1 << n - 1;
  let pv = -1;
  let mv = 0;
  let sc = n;
  let i = n;
  while (i--) {
    peq[a.charCodeAt(i)] |= 1 << i;
  }
  for (i = 0; i < m; i++) {
    let eq = peq[b.charCodeAt(i)];
    const xv = eq | mv;
    eq |= (eq & pv) + pv ^ pv;
    mv |= ~(eq | pv);
    pv &= eq;
    if (mv & lst) {
      sc++;
    }
    if (pv & lst) {
      sc--;
    }
    mv = mv << 1 | 1;
    pv = pv << 1 | ~(xv | mv);
    mv &= xv;
  }
  i = n;
  while (i--) {
    peq[a.charCodeAt(i)] = 0;
  }
  return sc;
};
var myers_x = (b, a) => {
  const n = a.length;
  const m = b.length;
  const mhc = [];
  const phc = [];
  const hsize = Math.ceil(n / 32);
  const vsize = Math.ceil(m / 32);
  for (let i = 0; i < hsize; i++) {
    phc[i] = -1;
    mhc[i] = 0;
  }
  let j = 0;
  for (; j < vsize - 1; j++) {
    let mv2 = 0;
    let pv2 = -1;
    const start2 = j * 32;
    const vlen2 = Math.min(32, m) + start2;
    for (let k = start2; k < vlen2; k++) {
      peq[b.charCodeAt(k)] |= 1 << k;
    }
    for (let i = 0; i < n; i++) {
      const eq = peq[a.charCodeAt(i)];
      const pb = phc[i / 32 | 0] >>> i & 1;
      const mb = mhc[i / 32 | 0] >>> i & 1;
      const xv = eq | mv2;
      const xh = ((eq | mb) & pv2) + pv2 ^ pv2 | eq | mb;
      let ph = mv2 | ~(xh | pv2);
      let mh = pv2 & xh;
      if (ph >>> 31 ^ pb) {
        phc[i / 32 | 0] ^= 1 << i;
      }
      if (mh >>> 31 ^ mb) {
        mhc[i / 32 | 0] ^= 1 << i;
      }
      ph = ph << 1 | pb;
      mh = mh << 1 | mb;
      pv2 = mh | ~(xv | ph);
      mv2 = ph & xv;
    }
    for (let k = start2; k < vlen2; k++) {
      peq[b.charCodeAt(k)] = 0;
    }
  }
  let mv = 0;
  let pv = -1;
  const start = j * 32;
  const vlen = Math.min(32, m - start) + start;
  for (let k = start; k < vlen; k++) {
    peq[b.charCodeAt(k)] |= 1 << k;
  }
  let score = m;
  for (let i = 0; i < n; i++) {
    const eq = peq[a.charCodeAt(i)];
    const pb = phc[i / 32 | 0] >>> i & 1;
    const mb = mhc[i / 32 | 0] >>> i & 1;
    const xv = eq | mv;
    const xh = ((eq | mb) & pv) + pv ^ pv | eq | mb;
    let ph = mv | ~(xh | pv);
    let mh = pv & xh;
    score += ph >>> m - 1 & 1;
    score -= mh >>> m - 1 & 1;
    if (ph >>> 31 ^ pb) {
      phc[i / 32 | 0] ^= 1 << i;
    }
    if (mh >>> 31 ^ mb) {
      mhc[i / 32 | 0] ^= 1 << i;
    }
    ph = ph << 1 | pb;
    mh = mh << 1 | mb;
    pv = mh | ~(xv | ph);
    mv = ph & xv;
  }
  for (let k = start; k < vlen; k++) {
    peq[b.charCodeAt(k)] = 0;
  }
  return score;
};
var distance = (a, b) => {
  if (a.length < b.length) {
    const tmp = b;
    b = a;
    a = tmp;
  }
  if (b.length === 0) {
    return a.length;
  }
  if (a.length <= 32) {
    return myers_32(a, b);
  }
  return myers_x(a, b);
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/levenshtein.esm.js
var getUsedThreshold = (password, entry, threshold) => {
  const isPasswordToShort = password.length <= entry.length;
  const isThresholdLongerThanPassword = password.length <= threshold;
  const shouldUsePasswordLength = isPasswordToShort || isThresholdLongerThanPassword;
  return shouldUsePasswordLength ? Math.ceil(password.length / 4) : threshold;
};
var findLevenshteinDistance = (password, rankedDictionary, threshold) => {
  let foundDistance = 0;
  const found = Object.keys(rankedDictionary).find((entry) => {
    const usedThreshold = getUsedThreshold(password, entry, threshold);
    if (Math.abs(password.length - entry.length) > usedThreshold) {
      return false;
    }
    const foundEntryDistance = distance(password, entry);
    const isInThreshold = foundEntryDistance <= usedThreshold;
    if (isInThreshold) {
      foundDistance = foundEntryDistance;
    }
    return isInThreshold;
  });
  if (found) {
    return {
      levenshteinDistance: foundDistance,
      levenshteinDistanceEntry: found
    };
  }
  return {};
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/data/l33tTable.esm.js
var l33tTable = {
  a: ["4", "@"],
  b: ["8"],
  c: ["(", "{", "[", "<"],
  d: ["6", "|)"],
  e: ["3"],
  f: ["#"],
  g: ["6", "9", "&"],
  h: ["#", "|-|"],
  i: ["1", "!", "|"],
  k: ["<", "|<"],
  l: ["!", "1", "|", "7"],
  m: ["^^", "nn", "2n", "/\\\\/\\\\"],
  n: ["//"],
  o: ["0", "()"],
  q: ["9"],
  u: ["|_|"],
  s: ["$", "5"],
  t: ["+", "7"],
  v: ["<", ">", "/"],
  w: ["^/", "uu", "vv", "2u", "2v", "\\\\/\\\\/"],
  x: ["%", "><"],
  z: ["2"]
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/data/translationKeys.esm.js
var translationKeys = {
  warnings: {
    straightRow: "straightRow",
    keyPattern: "keyPattern",
    simpleRepeat: "simpleRepeat",
    extendedRepeat: "extendedRepeat",
    sequences: "sequences",
    recentYears: "recentYears",
    dates: "dates",
    topTen: "topTen",
    topHundred: "topHundred",
    common: "common",
    similarToCommon: "similarToCommon",
    wordByItself: "wordByItself",
    namesByThemselves: "namesByThemselves",
    commonNames: "commonNames",
    userInputs: "userInputs",
    pwned: "pwned"
  },
  suggestions: {
    l33t: "l33t",
    reverseWords: "reverseWords",
    allUppercase: "allUppercase",
    capitalization: "capitalization",
    dates: "dates",
    recentYears: "recentYears",
    associatedYears: "associatedYears",
    sequences: "sequences",
    repeated: "repeated",
    longerKeyboardPattern: "longerKeyboardPattern",
    anotherWord: "anotherWord",
    useWords: "useWords",
    noNeed: "noNeed",
    pwned: "pwned"
  },
  timeEstimation: {
    ltSecond: "ltSecond",
    second: "second",
    seconds: "seconds",
    minute: "minute",
    minutes: "minutes",
    hour: "hour",
    hours: "hours",
    day: "day",
    days: "days",
    month: "month",
    months: "months",
    year: "year",
    years: "years",
    centuries: "centuries"
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/variants/matching/unmunger/TrieNode.esm.js
var TrieNode = class _TrieNode {
  constructor(parents = []) {
    this.parents = parents;
    this.children = /* @__PURE__ */ new Map();
  }
  addSub(key, ...subs) {
    const firstChar = key.charAt(0);
    if (!this.children.has(firstChar)) {
      this.children.set(firstChar, new _TrieNode([...this.parents, firstChar]));
    }
    let cur = this.children.get(firstChar);
    for (let i = 1; i < key.length; i += 1) {
      const c = key.charAt(i);
      if (!cur.hasChild(c)) {
        cur.addChild(c);
      }
      cur = cur.getChild(c);
    }
    cur.subs = (cur.subs || []).concat(subs);
    return this;
  }
  getChild(child) {
    return this.children.get(child);
  }
  isTerminal() {
    return !!this.subs;
  }
  addChild(child) {
    if (!this.hasChild(child)) {
      this.children.set(child, new _TrieNode([...this.parents, child]));
    }
  }
  hasChild(child) {
    return this.children.has(child);
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/variants/matching/unmunger/l33tTableToTrieNode.esm.js
var l33tTableToTrieNode = (l33tTable2, triNode) => {
  Object.entries(l33tTable2).forEach(([letter, substitutions]) => {
    substitutions.forEach((substitution) => {
      triNode.addSub(substitution, letter);
    });
  });
  return triNode;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/Options.esm.js
var Options = class {
  constructor() {
    this.matchers = {};
    this.l33tTable = l33tTable;
    this.trieNodeRoot = l33tTableToTrieNode(l33tTable, new TrieNode());
    this.dictionary = {
      userInputs: []
    };
    this.rankedDictionaries = {};
    this.rankedDictionariesMaxWordSize = {};
    this.translations = translationKeys;
    this.graphs = {};
    this.useLevenshteinDistance = false;
    this.levenshteinThreshold = 2;
    this.l33tMaxSubstitutions = 100;
    this.maxLength = 256;
    this.setRankedDictionaries();
  }
  // eslint-disable-next-line max-statements,complexity
  setOptions(options = {}) {
    if (options.l33tTable) {
      this.l33tTable = options.l33tTable;
      this.trieNodeRoot = l33tTableToTrieNode(options.l33tTable, new TrieNode());
    }
    if (options.dictionary) {
      this.dictionary = options.dictionary;
      this.setRankedDictionaries();
    }
    if (options.translations) {
      this.setTranslations(options.translations);
    }
    if (options.graphs) {
      this.graphs = options.graphs;
    }
    if (options.useLevenshteinDistance !== void 0) {
      this.useLevenshteinDistance = options.useLevenshteinDistance;
    }
    if (options.levenshteinThreshold !== void 0) {
      this.levenshteinThreshold = options.levenshteinThreshold;
    }
    if (options.l33tMaxSubstitutions !== void 0) {
      this.l33tMaxSubstitutions = options.l33tMaxSubstitutions;
    }
    if (options.maxLength !== void 0) {
      this.maxLength = options.maxLength;
    }
  }
  setTranslations(translations) {
    if (this.checkCustomTranslations(translations)) {
      this.translations = translations;
    } else {
      throw new Error("Invalid translations object fallback to keys");
    }
  }
  checkCustomTranslations(translations) {
    let valid = true;
    Object.keys(translationKeys).forEach((type) => {
      if (type in translations) {
        const translationType = type;
        Object.keys(translationKeys[translationType]).forEach((key) => {
          if (!(key in translations[translationType])) {
            valid = false;
          }
        });
      } else {
        valid = false;
      }
    });
    return valid;
  }
  setRankedDictionaries() {
    const rankedDictionaries = {};
    const rankedDictionariesMaxWorkSize = {};
    Object.keys(this.dictionary).forEach((name) => {
      rankedDictionaries[name] = buildRankedDictionary(this.dictionary[name]);
      rankedDictionariesMaxWorkSize[name] = this.getRankedDictionariesMaxWordSize(this.dictionary[name]);
    });
    this.rankedDictionaries = rankedDictionaries;
    this.rankedDictionariesMaxWordSize = rankedDictionariesMaxWorkSize;
  }
  getRankedDictionariesMaxWordSize(list) {
    const data = list.map((el) => {
      if (typeof el !== "string") {
        return el.toString().length;
      }
      return el.length;
    });
    if (data.length === 0) {
      return 0;
    }
    return data.reduce((a, b) => Math.max(a, b), -Infinity);
  }
  buildSanitizedRankedDictionary(list) {
    const sanitizedInputs = [];
    list.forEach((input) => {
      const inputType = typeof input;
      if (inputType === "string" || inputType === "number" || inputType === "boolean") {
        sanitizedInputs.push(input.toString().toLowerCase());
      }
    });
    return buildRankedDictionary(sanitizedInputs);
  }
  extendUserInputsDictionary(dictionary) {
    if (!this.dictionary.userInputs) {
      this.dictionary.userInputs = [];
    }
    const newList = [...this.dictionary.userInputs, ...dictionary];
    this.rankedDictionaries.userInputs = this.buildSanitizedRankedDictionary(newList);
    this.rankedDictionariesMaxWordSize.userInputs = this.getRankedDictionariesMaxWordSize(newList);
  }
  addMatcher(name, matcher) {
    if (this.matchers[name]) {
      console.info(`Matcher ${name} already exists`);
    } else {
      this.matchers[name] = matcher;
    }
  }
};
var zxcvbnOptions = new Options();

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/variants/matching/reverse.esm.js
var MatchReverse = class {
  constructor(defaultMatch) {
    this.defaultMatch = defaultMatch;
  }
  match({
    password
  }) {
    const passwordReversed = password.split("").reverse().join("");
    return this.defaultMatch({
      password: passwordReversed
    }).map((match) => ({
      ...match,
      token: match.token.split("").reverse().join(""),
      reversed: true,
      // map coordinates back to original string
      i: password.length - 1 - match.j,
      j: password.length - 1 - match.i
    }));
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/variants/matching/unmunger/getCleanPasswords.esm.js
var CleanPasswords = class {
  constructor({
    substr,
    limit,
    trieRoot
  }) {
    this.buffer = [];
    this.finalPasswords = [];
    this.substr = substr;
    this.limit = limit;
    this.trieRoot = trieRoot;
  }
  getAllPossibleSubsAtIndex(index) {
    const nodes = [];
    let cur = this.trieRoot;
    for (let i = index; i < this.substr.length; i += 1) {
      const character = this.substr.charAt(i);
      cur = cur.getChild(character);
      if (!cur) {
        break;
      }
      nodes.push(cur);
    }
    return nodes;
  }
  // eslint-disable-next-line complexity,max-statements
  helper({
    onlyFullSub,
    isFullSub,
    index,
    subIndex,
    changes,
    lastSubLetter,
    consecutiveSubCount
  }) {
    if (this.finalPasswords.length >= this.limit) {
      return;
    }
    if (index === this.substr.length) {
      if (onlyFullSub === isFullSub) {
        this.finalPasswords.push({
          password: this.buffer.join(""),
          changes
        });
      }
      return;
    }
    const nodes = [...this.getAllPossibleSubsAtIndex(index)];
    let hasSubs = false;
    for (let i = index + nodes.length - 1; i >= index; i -= 1) {
      const cur = nodes[i - index];
      if (cur.isTerminal()) {
        if (lastSubLetter === cur.parents.join("") && consecutiveSubCount >= 3) {
          continue;
        }
        hasSubs = true;
        const subs = cur.subs;
        for (const sub of subs) {
          this.buffer.push(sub);
          const newSubs = changes.concat({
            i: subIndex,
            letter: sub,
            substitution: cur.parents.join("")
          });
          this.helper({
            onlyFullSub,
            isFullSub,
            index: i + 1,
            subIndex: subIndex + sub.length,
            changes: newSubs,
            lastSubLetter: cur.parents.join(""),
            consecutiveSubCount: lastSubLetter === cur.parents.join("") ? consecutiveSubCount + 1 : 1
          });
          this.buffer.pop();
          if (this.finalPasswords.length >= this.limit) {
            return;
          }
        }
      }
    }
    if (!onlyFullSub || !hasSubs) {
      const firstChar = this.substr.charAt(index);
      this.buffer.push(firstChar);
      this.helper({
        onlyFullSub,
        isFullSub: isFullSub && !hasSubs,
        index: index + 1,
        subIndex: subIndex + 1,
        changes,
        lastSubLetter,
        consecutiveSubCount
      });
      this.buffer.pop();
    }
  }
  getAll() {
    this.helper({
      onlyFullSub: true,
      isFullSub: true,
      index: 0,
      subIndex: 0,
      changes: [],
      lastSubLetter: void 0,
      consecutiveSubCount: 0
    });
    this.helper({
      onlyFullSub: false,
      isFullSub: true,
      index: 0,
      subIndex: 0,
      changes: [],
      lastSubLetter: void 0,
      consecutiveSubCount: 0
    });
    return this.finalPasswords;
  }
};
var getCleanPasswords = (password, limit, trieRoot) => {
  const helper = new CleanPasswords({
    substr: password,
    limit,
    trieRoot
  });
  return helper.getAll();
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/variants/matching/l33t.esm.js
var getExtras = (passwordWithSubs, i, j) => {
  const previousChanges = passwordWithSubs.changes.filter((changes) => {
    return changes.i < i;
  });
  const iUnsubbed = previousChanges.reduce((value, change) => {
    return value - change.letter.length + change.substitution.length;
  }, i);
  const usedChanges = passwordWithSubs.changes.filter((changes) => {
    return changes.i >= i && changes.i <= j;
  });
  const jUnsubbed = usedChanges.reduce((value, change) => {
    return value - change.letter.length + change.substitution.length;
  }, j - i + iUnsubbed);
  const filtered = [];
  const subDisplay = [];
  usedChanges.forEach((value) => {
    const existingIndex = filtered.findIndex((t) => {
      return t.letter === value.letter && t.substitution === value.substitution;
    });
    if (existingIndex < 0) {
      filtered.push({
        letter: value.letter,
        substitution: value.substitution
      });
      subDisplay.push(`${value.substitution} -> ${value.letter}`);
    }
  });
  return {
    i: iUnsubbed,
    j: jUnsubbed,
    subs: filtered,
    subDisplay: subDisplay.join(", ")
  };
};
var MatchL33t = class {
  constructor(defaultMatch) {
    this.defaultMatch = defaultMatch;
  }
  isAlreadyIncluded(matches, newMatch) {
    return matches.some((l33tMatch) => {
      return Object.entries(l33tMatch).every(([key, value]) => {
        return key === "subs" || value === newMatch[key];
      });
    });
  }
  match({
    password
  }) {
    const matches = [];
    const subbedPasswords = getCleanPasswords(password, zxcvbnOptions.l33tMaxSubstitutions, zxcvbnOptions.trieNodeRoot);
    let hasFullMatch = false;
    let isFullSubstitution = true;
    subbedPasswords.forEach((subbedPassword) => {
      if (hasFullMatch) {
        return;
      }
      const matchedDictionary = this.defaultMatch({
        password: subbedPassword.password,
        useLevenshtein: isFullSubstitution
      });
      isFullSubstitution = false;
      matchedDictionary.forEach((match) => {
        if (!hasFullMatch) {
          hasFullMatch = match.i === 0 && match.j === password.length - 1;
        }
        const extras = getExtras(subbedPassword, match.i, match.j);
        const token = password.slice(extras.i, +extras.j + 1 || 9e9);
        const newMatch = {
          ...match,
          l33t: true,
          token,
          ...extras
        };
        const alreadyIncluded = this.isAlreadyIncluded(matches, newMatch);
        if (token.toLowerCase() !== match.matchedWord && !alreadyIncluded) {
          matches.push(newMatch);
        }
      });
    });
    return matches.filter((match) => match.token.length > 1);
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/matching.esm.js
var MatchDictionary = class {
  constructor() {
    this.l33t = new MatchL33t(this.defaultMatch);
    this.reverse = new MatchReverse(this.defaultMatch);
  }
  match({
    password
  }) {
    const matches = [...this.defaultMatch({
      password
    }), ...this.reverse.match({
      password
    }), ...this.l33t.match({
      password
    })];
    return sorted(matches);
  }
  defaultMatch({
    password,
    useLevenshtein = true
  }) {
    const matches = [];
    const passwordLength = password.length;
    const passwordLower = password.toLowerCase();
    Object.keys(zxcvbnOptions.rankedDictionaries).forEach((dictionaryName) => {
      const rankedDict = zxcvbnOptions.rankedDictionaries[dictionaryName];
      const longestDictionaryWordSize = zxcvbnOptions.rankedDictionariesMaxWordSize[dictionaryName];
      const searchWidth = Math.min(longestDictionaryWordSize, passwordLength);
      for (let i = 0; i < passwordLength; i += 1) {
        const searchEnd = Math.min(i + searchWidth, passwordLength);
        for (let j = i; j < searchEnd; j += 1) {
          const usedPassword = passwordLower.slice(i, +j + 1 || 9e9);
          const isInDictionary = usedPassword in rankedDict;
          let foundLevenshteinDistance = {};
          const isFullPassword = i === 0 && j === passwordLength - 1;
          if (zxcvbnOptions.useLevenshteinDistance && isFullPassword && !isInDictionary && useLevenshtein) {
            foundLevenshteinDistance = findLevenshteinDistance(usedPassword, rankedDict, zxcvbnOptions.levenshteinThreshold);
          }
          const isLevenshteinMatch = Object.keys(foundLevenshteinDistance).length !== 0;
          if (isInDictionary || isLevenshteinMatch) {
            const usedRankPassword = isLevenshteinMatch ? foundLevenshteinDistance.levenshteinDistanceEntry : usedPassword;
            const rank = rankedDict[usedRankPassword];
            matches.push({
              pattern: "dictionary",
              i,
              j,
              token: password.slice(i, +j + 1 || 9e9),
              matchedWord: usedPassword,
              rank,
              dictionaryName,
              reversed: false,
              l33t: false,
              ...foundLevenshteinDistance
            });
          }
        }
      }
    });
    return matches;
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/regex/matching.esm.js
var MatchRegex = class {
  match({
    password,
    regexes = REGEXEN
  }) {
    const matches = [];
    Object.keys(regexes).forEach((name) => {
      const regex = regexes[name];
      regex.lastIndex = 0;
      let regexMatch;
      while (regexMatch = regex.exec(password)) {
        if (regexMatch) {
          const token = regexMatch[0];
          matches.push({
            pattern: "regex",
            token,
            i: regexMatch.index,
            j: regexMatch.index + regexMatch[0].length - 1,
            regexName: name,
            regexMatch
          });
        }
      }
    });
    return sorted(matches);
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/scoring/utils.esm.js
var utils = {
  // binomial coefficients
  // src: http://blog.plover.com/math/choose.html
  nCk(n, k) {
    let count = n;
    if (k > count) {
      return 0;
    }
    if (k === 0) {
      return 1;
    }
    let coEff = 1;
    for (let i = 1; i <= k; i += 1) {
      coEff *= count;
      coEff /= i;
      count -= 1;
    }
    return coEff;
  },
  log10(n) {
    if (n === 0) return 0;
    return Math.log(n) / Math.log(10);
  },
  log2(n) {
    return Math.log(n) / Math.log(2);
  },
  factorial(num) {
    let rval = 1;
    for (let i = 2; i <= num; i += 1) rval *= i;
    return rval;
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/bruteforce/scoring.esm.js
var bruteforceMatcher = ({
  token
}) => {
  let guesses = BRUTEFORCE_CARDINALITY ** token.length;
  if (guesses === Number.POSITIVE_INFINITY) {
    guesses = Number.MAX_VALUE;
  }
  let minGuesses;
  if (token.length === 1) {
    minGuesses = MIN_SUBMATCH_GUESSES_SINGLE_CHAR + 1;
  } else {
    minGuesses = MIN_SUBMATCH_GUESSES_MULTI_CHAR + 1;
  }
  return Math.max(guesses, minGuesses);
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/date/scoring.esm.js
var dateMatcher = ({
  year,
  separator
}) => {
  const yearSpace = Math.max(Math.abs(year - REFERENCE_YEAR), MIN_YEAR_SPACE);
  let guesses = yearSpace * 365;
  if (separator) {
    guesses *= 4;
  }
  return guesses;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/variants/scoring/uppercase.esm.js
var getVariations = (cleanedWord) => {
  const wordArray = cleanedWord.split("");
  const upperCaseCount = wordArray.filter((char) => char.match(ONE_UPPER)).length;
  const lowerCaseCount = wordArray.filter((char) => char.match(ONE_LOWER)).length;
  let variations = 0;
  const variationLength = Math.min(upperCaseCount, lowerCaseCount);
  for (let i = 1; i <= variationLength; i += 1) {
    variations += utils.nCk(upperCaseCount + lowerCaseCount, i);
  }
  return variations;
};
var uppercaseVariant = (word) => {
  const cleanedWord = word.replace(ALPHA_INVERTED, "");
  if (cleanedWord.match(ALL_LOWER_INVERTED) || cleanedWord.toLowerCase() === cleanedWord) {
    return 1;
  }
  const commonCases = [START_UPPER, END_UPPER, ALL_UPPER_INVERTED];
  const commonCasesLength = commonCases.length;
  for (let i = 0; i < commonCasesLength; i += 1) {
    const regex = commonCases[i];
    if (cleanedWord.match(regex)) {
      return 2;
    }
  }
  return getVariations(cleanedWord);
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/variants/scoring/l33t.esm.js
var countSubstring = (string, substring) => {
  let count = 0;
  let pos = string.indexOf(substring);
  while (pos >= 0) {
    count += 1;
    pos = string.indexOf(substring, pos + substring.length);
  }
  return count;
};
var getCounts = ({
  sub,
  token
}) => {
  const tokenLower = token.toLowerCase();
  const subbedCount = countSubstring(tokenLower, sub.substitution);
  const unsubbedCount = countSubstring(tokenLower, sub.letter);
  return {
    subbedCount,
    unsubbedCount
  };
};
var l33tVariant = ({
  l33t,
  subs,
  token
}) => {
  if (!l33t) {
    return 1;
  }
  let variations = 1;
  subs.forEach((sub) => {
    const {
      subbedCount,
      unsubbedCount
    } = getCounts({
      sub,
      token
    });
    if (subbedCount === 0 || unsubbedCount === 0) {
      variations *= 2;
    } else {
      const p = Math.min(unsubbedCount, subbedCount);
      let possibilities = 0;
      for (let i = 1; i <= p; i += 1) {
        possibilities += utils.nCk(unsubbedCount + subbedCount, i);
      }
      variations *= possibilities;
    }
  });
  return variations;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/scoring.esm.js
var dictionaryMatcher = ({
  rank,
  reversed,
  l33t,
  subs,
  token,
  dictionaryName
}) => {
  const baseGuesses = rank;
  const uppercaseVariations = uppercaseVariant(token);
  const l33tVariations = l33tVariant({
    l33t,
    subs,
    token
  });
  const reversedVariations = reversed && 2 || 1;
  let calculation;
  if (dictionaryName === "diceware") {
    calculation = 6 ** 5 / 2;
  } else {
    calculation = baseGuesses * uppercaseVariations * l33tVariations * reversedVariations;
  }
  return {
    baseGuesses,
    uppercaseVariations,
    l33tVariations,
    calculation
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/regex/scoring.esm.js
var regexMatcher = ({
  regexName,
  regexMatch,
  token
}) => {
  const charClassBases = {
    alphaLower: 26,
    alphaUpper: 26,
    alpha: 52,
    alphanumeric: 62,
    digits: 10,
    symbols: 33
  };
  if (regexName in charClassBases) {
    return charClassBases[regexName] ** token.length;
  }
  switch (regexName) {
    case "recentYear":
      return Math.max(Math.abs(parseInt(regexMatch[0], 10) - REFERENCE_YEAR), MIN_YEAR_SPACE);
  }
  return 0;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/repeat/scoring.esm.js
var repeatMatcher = ({
  baseGuesses,
  repeatCount
}) => baseGuesses * repeatCount;

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/sequence/scoring.esm.js
var sequenceMatcher = ({
  token,
  ascending
}) => {
  const firstChr = token.charAt(0);
  let baseGuesses = 0;
  const startingPoints = ["a", "A", "z", "Z", "0", "1", "9"];
  if (startingPoints.includes(firstChr)) {
    baseGuesses = 4;
  } else if (firstChr.match(/\d/)) {
    baseGuesses = 10;
  } else {
    baseGuesses = 26;
  }
  if (!ascending) {
    baseGuesses *= 2;
  }
  return baseGuesses * token.length;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/spatial/scoring.esm.js
var calcAverageDegree = (graph) => {
  let average = 0;
  Object.keys(graph).forEach((key) => {
    const neighbors = graph[key];
    average += neighbors.filter((entry) => !!entry).length;
  });
  average /= Object.entries(graph).length;
  return average;
};
var estimatePossiblePatterns = ({
  token,
  graph,
  turns
}) => {
  const startingPosition = Object.keys(zxcvbnOptions.graphs[graph]).length;
  const averageDegree = calcAverageDegree(zxcvbnOptions.graphs[graph]);
  let guesses = 0;
  const tokenLength = token.length;
  for (let i = 2; i <= tokenLength; i += 1) {
    const possibleTurns = Math.min(turns, i - 1);
    for (let j = 1; j <= possibleTurns; j += 1) {
      guesses += utils.nCk(i - 1, j - 1) * startingPosition * averageDegree ** j;
    }
  }
  return guesses;
};
var spatialMatcher = ({
  graph,
  token,
  shiftedCount,
  turns
}) => {
  let guesses = estimatePossiblePatterns({
    token,
    graph,
    turns
  });
  if (shiftedCount) {
    const unShiftedCount = token.length - shiftedCount;
    if (shiftedCount === 0 || unShiftedCount === 0) {
      guesses *= 2;
    } else {
      let shiftedVariations = 0;
      for (let i = 1; i <= Math.min(shiftedCount, unShiftedCount); i += 1) {
        shiftedVariations += utils.nCk(shiftedCount + unShiftedCount, i);
      }
      guesses *= shiftedVariations;
    }
  }
  return Math.round(guesses);
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/separator/scoring.esm.js
var separatorMatcher = () => {
  return SEPERATOR_CHAR_COUNT;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/scoring/estimate.esm.js
var getMinGuesses = (match, password) => {
  let minGuesses = 1;
  if (match.token.length < password.length) {
    if (match.token.length === 1) {
      minGuesses = MIN_SUBMATCH_GUESSES_SINGLE_CHAR;
    } else {
      minGuesses = MIN_SUBMATCH_GUESSES_MULTI_CHAR;
    }
  }
  return minGuesses;
};
var matchers = {
  bruteforce: bruteforceMatcher,
  date: dateMatcher,
  dictionary: dictionaryMatcher,
  regex: regexMatcher,
  repeat: repeatMatcher,
  sequence: sequenceMatcher,
  spatial: spatialMatcher,
  separator: separatorMatcher
};
var getScoring = (name, match) => {
  if (matchers[name]) {
    return matchers[name](match);
  }
  if (zxcvbnOptions.matchers[name] && "scoring" in zxcvbnOptions.matchers[name]) {
    return zxcvbnOptions.matchers[name].scoring(match);
  }
  return 0;
};
var estimateGuesses = (match, password) => {
  const extraData = {};
  if ("guesses" in match && match.guesses != null) {
    return match;
  }
  const minGuesses = getMinGuesses(match, password);
  const estimationResult = getScoring(match.pattern, match);
  let guesses = 0;
  if (typeof estimationResult === "number") {
    guesses = estimationResult;
  } else if (match.pattern === "dictionary") {
    guesses = estimationResult.calculation;
    extraData.baseGuesses = estimationResult.baseGuesses;
    extraData.uppercaseVariations = estimationResult.uppercaseVariations;
    extraData.l33tVariations = estimationResult.l33tVariations;
  }
  const matchGuesses = Math.max(guesses, minGuesses);
  return {
    ...match,
    ...extraData,
    guesses: matchGuesses,
    guessesLog10: utils.log10(matchGuesses)
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/scoring/index.esm.js
var scoringHelper = {
  password: "",
  optimal: {},
  excludeAdditive: false,
  separatorRegex: void 0,
  fillArray(size, valueType) {
    const result = [];
    for (let i = 0; i < size; i += 1) {
      let value = [];
      if (valueType === "object") {
        value = {};
      }
      result.push(value);
    }
    return result;
  },
  // helper: make bruteforce match objects spanning i to j, inclusive.
  makeBruteforceMatch(i, j) {
    return {
      pattern: "bruteforce",
      token: this.password.slice(i, +j + 1 || 9e9),
      i,
      j
    };
  },
  // helper: considers whether a length-sequenceLength
  // sequence ending at match m is better (fewer guesses)
  // than previously encountered sequences, updating state if so.
  update(match, sequenceLength) {
    const k = match.j;
    const estimatedMatch = estimateGuesses(match, this.password);
    let pi = estimatedMatch.guesses;
    if (sequenceLength > 1) {
      pi *= this.optimal.pi[estimatedMatch.i - 1][sequenceLength - 1];
    }
    let g = utils.factorial(sequenceLength) * pi;
    if (!this.excludeAdditive) {
      g += MIN_GUESSES_BEFORE_GROWING_SEQUENCE ** (sequenceLength - 1);
    }
    let shouldSkip = false;
    Object.keys(this.optimal.g[k]).forEach((competingPatternLength) => {
      const competingMetricMatch = this.optimal.g[k][competingPatternLength];
      if (parseInt(competingPatternLength, 10) <= sequenceLength) {
        if (competingMetricMatch <= g) {
          shouldSkip = true;
        }
      }
    });
    if (!shouldSkip) {
      this.optimal.g[k][sequenceLength] = g;
      this.optimal.m[k][sequenceLength] = estimatedMatch;
      this.optimal.pi[k][sequenceLength] = pi;
    }
  },
  // helper: evaluate bruteforce matches ending at passwordCharIndex.
  bruteforceUpdate(passwordCharIndex) {
    let match = this.makeBruteforceMatch(0, passwordCharIndex);
    this.update(match, 1);
    for (let i = 1; i <= passwordCharIndex; i += 1) {
      match = this.makeBruteforceMatch(i, passwordCharIndex);
      const tmp = this.optimal.m[i - 1];
      Object.keys(tmp).forEach((sequenceLength) => {
        const lastMatch = tmp[sequenceLength];
        if (lastMatch.pattern !== "bruteforce") {
          this.update(match, parseInt(sequenceLength, 10) + 1);
        }
      });
    }
  },
  // helper: step backwards through optimal.m starting at the end,
  // constructing the final optimal match sequence.
  unwind(passwordLength) {
    const optimalMatchSequence = [];
    let k = passwordLength - 1;
    let sequenceLength = 0;
    let g = Infinity;
    const temp = this.optimal.g[k];
    if (temp) {
      Object.keys(temp).forEach((candidateSequenceLength) => {
        const candidateMetricMatch = temp[candidateSequenceLength];
        if (candidateMetricMatch < g) {
          sequenceLength = parseInt(candidateSequenceLength, 10);
          g = candidateMetricMatch;
        }
      });
    }
    while (k >= 0) {
      const match = this.optimal.m[k][sequenceLength];
      optimalMatchSequence.unshift(match);
      k = match.i - 1;
      sequenceLength -= 1;
    }
    return optimalMatchSequence;
  }
};
var scoring = {
  // ------------------------------------------------------------------------------
  // search --- most guessable match sequence -------------------------------------
  // ------------------------------------------------------------------------------
  //
  // takes a sequence of overlapping matches, returns the non-overlapping sequence with
  // minimum guesses. the following is a O(l_max * (n + m)) dynamic programming algorithm
  // for a length-n password with m candidate matches. l_max is the maximum optimal
  // sequence length spanning each prefix of the password. In practice it rarely exceeds 5 and the
  // search terminates rapidly.
  //
  // the optimal "minimum guesses" sequence is here defined to be the sequence that
  // minimizes the following function:
  //
  //    g = sequenceLength! * Product(m.guesses for m in sequence) + D^(sequenceLength - 1)
  //
  // where sequenceLength is the length of the sequence.
  //
  // the factorial term is the number of ways to order sequenceLength patterns.
  //
  // the D^(sequenceLength-1) term is another length penalty, roughly capturing the idea that an
  // attacker will try lower-length sequences first before trying length-sequenceLength sequences.
  //
  // for example, consider a sequence that is date-repeat-dictionary.
  //  - an attacker would need to try other date-repeat-dictionary combinations,
  //    hence the product term.
  //  - an attacker would need to try repeat-date-dictionary, dictionary-repeat-date,
  //    ..., hence the factorial term.
  //  - an attacker would also likely try length-1 (dictionary) and length-2 (dictionary-date)
  //    sequences before length-3. assuming at minimum D guesses per pattern type,
  //    D^(sequenceLength-1) approximates Sum(D^i for i in [1..sequenceLength-1]
  //
  // ------------------------------------------------------------------------------
  mostGuessableMatchSequence(password, matches, excludeAdditive = false) {
    scoringHelper.password = password;
    scoringHelper.excludeAdditive = excludeAdditive;
    const passwordLength = password.length;
    let matchesByCoordinateJ = scoringHelper.fillArray(passwordLength, "array");
    matches.forEach((match) => {
      matchesByCoordinateJ[match.j].push(match);
    });
    matchesByCoordinateJ = matchesByCoordinateJ.map((match) => match.sort((m1, m2) => m1.i - m2.i));
    scoringHelper.optimal = {
      // optimal.m[k][sequenceLength] holds final match in the best length-sequenceLength
      // match sequence covering the
      // password prefix up to k, inclusive.
      // if there is no length-sequenceLength sequence that scores better (fewer guesses) than
      // a shorter match sequence spanning the same prefix,
      // optimal.m[k][sequenceLength] is undefined.
      m: scoringHelper.fillArray(passwordLength, "object"),
      // same structure as optimal.m -- holds the product term Prod(m.guesses for m in sequence).
      // optimal.pi allows for fast (non-looping) updates to the minimization function.
      pi: scoringHelper.fillArray(passwordLength, "object"),
      // same structure as optimal.m -- holds the overall metric.
      g: scoringHelper.fillArray(passwordLength, "object")
    };
    for (let k = 0; k < passwordLength; k += 1) {
      matchesByCoordinateJ[k].forEach((match) => {
        if (match.i > 0) {
          Object.keys(scoringHelper.optimal.m[match.i - 1]).forEach((sequenceLength) => {
            scoringHelper.update(match, parseInt(sequenceLength, 10) + 1);
          });
        } else {
          scoringHelper.update(match, 1);
        }
      });
      scoringHelper.bruteforceUpdate(k);
    }
    const optimalMatchSequence = scoringHelper.unwind(passwordLength);
    const optimalSequenceLength = optimalMatchSequence.length;
    const guesses = this.getGuesses(password, optimalSequenceLength);
    return {
      password,
      guesses,
      guessesLog10: utils.log10(guesses),
      sequence: optimalMatchSequence
    };
  },
  getGuesses(password, optimalSequenceLength) {
    const passwordLength = password.length;
    let guesses = 0;
    if (password.length === 0) {
      guesses = 1;
    } else {
      guesses = scoringHelper.optimal.g[passwordLength - 1][optimalSequenceLength];
    }
    return guesses;
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/repeat/matching.esm.js
var MatchRepeat = class {
  // eslint-disable-next-line max-statements
  match({
    password,
    omniMatch
  }) {
    const matches = [];
    let lastIndex = 0;
    while (lastIndex < password.length) {
      const greedyMatch = this.getGreedyMatch(password, lastIndex);
      const lazyMatch = this.getLazyMatch(password, lastIndex);
      if (greedyMatch == null) {
        break;
      }
      const {
        match,
        baseToken
      } = this.setMatchToken(greedyMatch, lazyMatch);
      if (match) {
        const j = match.index + match[0].length - 1;
        const baseGuesses = this.getBaseGuesses(baseToken, omniMatch);
        matches.push(this.normalizeMatch(baseToken, j, match, baseGuesses));
        lastIndex = j + 1;
      }
    }
    const hasPromises = matches.some((match) => {
      return match instanceof Promise;
    });
    if (hasPromises) {
      return Promise.all(matches);
    }
    return matches;
  }
  // eslint-disable-next-line max-params
  normalizeMatch(baseToken, j, match, baseGuesses) {
    const baseMatch = {
      pattern: "repeat",
      i: match.index,
      j,
      token: match[0],
      baseToken,
      baseGuesses: 0,
      repeatCount: match[0].length / baseToken.length
    };
    if (baseGuesses instanceof Promise) {
      return baseGuesses.then((resolvedBaseGuesses) => {
        return {
          ...baseMatch,
          baseGuesses: resolvedBaseGuesses
        };
      });
    }
    return {
      ...baseMatch,
      baseGuesses
    };
  }
  getGreedyMatch(password, lastIndex) {
    const greedy = /(.+)\1+/g;
    greedy.lastIndex = lastIndex;
    return greedy.exec(password);
  }
  getLazyMatch(password, lastIndex) {
    const lazy = /(.+?)\1+/g;
    lazy.lastIndex = lastIndex;
    return lazy.exec(password);
  }
  setMatchToken(greedyMatch, lazyMatch) {
    const lazyAnchored = /^(.+?)\1+$/;
    let match;
    let baseToken = "";
    if (lazyMatch && greedyMatch[0].length > lazyMatch[0].length) {
      match = greedyMatch;
      const temp = lazyAnchored.exec(match[0]);
      if (temp) {
        baseToken = temp[1];
      }
    } else {
      match = lazyMatch;
      if (match) {
        baseToken = match[1];
      }
    }
    return {
      match,
      baseToken
    };
  }
  getBaseGuesses(baseToken, omniMatch) {
    const matches = omniMatch.match(baseToken);
    if (matches instanceof Promise) {
      return matches.then((resolvedMatches) => {
        const baseAnalysis2 = scoring.mostGuessableMatchSequence(baseToken, resolvedMatches);
        return baseAnalysis2.guesses;
      });
    }
    const baseAnalysis = scoring.mostGuessableMatchSequence(baseToken, matches);
    return baseAnalysis.guesses;
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/sequence/matching.esm.js
var MatchSequence = class {
  constructor() {
    this.MAX_DELTA = 5;
  }
  // eslint-disable-next-line max-statements
  match({
    password
  }) {
    const result = [];
    if (password.length === 1) {
      return [];
    }
    let i = 0;
    let lastDelta = null;
    const passwordLength = password.length;
    for (let k = 1; k < passwordLength; k += 1) {
      const delta = password.charCodeAt(k) - password.charCodeAt(k - 1);
      if (lastDelta == null) {
        lastDelta = delta;
      }
      if (delta !== lastDelta) {
        const j = k - 1;
        this.update({
          i,
          j,
          delta: lastDelta,
          password,
          result
        });
        i = j;
        lastDelta = delta;
      }
    }
    this.update({
      i,
      j: passwordLength - 1,
      delta: lastDelta,
      password,
      result
    });
    return result;
  }
  update({
    i,
    j,
    delta,
    password,
    result
  }) {
    if (j - i > 1 || Math.abs(delta) === 1) {
      const absoluteDelta = Math.abs(delta);
      if (absoluteDelta > 0 && absoluteDelta <= this.MAX_DELTA) {
        const token = password.slice(i, +j + 1 || 9e9);
        const {
          sequenceName,
          sequenceSpace
        } = this.getSequence(token);
        return result.push({
          pattern: "sequence",
          i,
          j,
          token: password.slice(i, +j + 1 || 9e9),
          sequenceName,
          sequenceSpace,
          ascending: delta > 0
        });
      }
    }
    return null;
  }
  getSequence(token) {
    let sequenceName = "unicode";
    let sequenceSpace = 26;
    if (ALL_LOWER.test(token)) {
      sequenceName = "lower";
      sequenceSpace = 26;
    } else if (ALL_UPPER.test(token)) {
      sequenceName = "upper";
      sequenceSpace = 26;
    } else if (ALL_DIGIT.test(token)) {
      sequenceName = "digits";
      sequenceSpace = 10;
    }
    return {
      sequenceName,
      sequenceSpace
    };
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/spatial/matching.esm.js
var MatchSpatial = class {
  constructor() {
    this.SHIFTED_RX = /[~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:"ZXCVBNM<>?]/;
  }
  match({
    password
  }) {
    const matches = [];
    Object.keys(zxcvbnOptions.graphs).forEach((graphName) => {
      const graph = zxcvbnOptions.graphs[graphName];
      extend(matches, this.helper(password, graph, graphName));
    });
    return sorted(matches);
  }
  checkIfShifted(graphName, password, index) {
    if (!graphName.includes("keypad") && // initial character is shifted
    this.SHIFTED_RX.test(password.charAt(index))) {
      return 1;
    }
    return 0;
  }
  // eslint-disable-next-line complexity, max-statements
  helper(password, graph, graphName) {
    let shiftedCount;
    const matches = [];
    let i = 0;
    const passwordLength = password.length;
    while (i < passwordLength - 1) {
      let j = i + 1;
      let lastDirection = null;
      let turns = 0;
      shiftedCount = this.checkIfShifted(graphName, password, i);
      while (true) {
        const prevChar = password.charAt(j - 1);
        const adjacents = graph[prevChar] || [];
        let found = false;
        let foundDirection = -1;
        let curDirection = -1;
        if (j < passwordLength) {
          const curChar = password.charAt(j);
          const adjacentsLength = adjacents.length;
          for (let k = 0; k < adjacentsLength; k += 1) {
            const adjacent = adjacents[k];
            curDirection += 1;
            if (adjacent) {
              const adjacentIndex = adjacent.indexOf(curChar);
              if (adjacentIndex !== -1) {
                found = true;
                foundDirection = curDirection;
                if (adjacentIndex === 1) {
                  shiftedCount += 1;
                }
                if (lastDirection !== foundDirection) {
                  turns += 1;
                  lastDirection = foundDirection;
                }
                break;
              }
            }
          }
        }
        if (found) {
          j += 1;
        } else {
          if (j - i > 2) {
            matches.push({
              pattern: "spatial",
              i,
              j: j - 1,
              token: password.slice(i, j),
              graph: graphName,
              turns,
              shiftedCount
            });
          }
          i = j;
          break;
        }
      }
    }
    return matches;
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/separator/matching.esm.js
var separatorRegex = new RegExp(`[${SEPERATOR_CHARS.join("")}]`);
var MatchSeparator = class _MatchSeparator {
  static getMostUsedSeparatorChar(password) {
    const mostUsedSeperators = [...password.split("").filter((c) => separatorRegex.test(c)).reduce((memo, c) => {
      const m = memo.get(c);
      if (m) {
        memo.set(c, m + 1);
      } else {
        memo.set(c, 1);
      }
      return memo;
    }, /* @__PURE__ */ new Map()).entries()].sort(([_a, a], [_b, b]) => b - a);
    if (!mostUsedSeperators.length) return void 0;
    const match = mostUsedSeperators[0];
    if (match[1] < 2) return void 0;
    return match[0];
  }
  static getSeparatorRegex(separator) {
    return new RegExp(`([^${separator}
])(${separator})(?!${separator})`, "g");
  }
  // eslint-disable-next-line max-statements
  match({
    password
  }) {
    const result = [];
    if (password.length === 0) return result;
    const mostUsedSpecial = _MatchSeparator.getMostUsedSeparatorChar(password);
    if (mostUsedSpecial === void 0) return result;
    const isSeparator = _MatchSeparator.getSeparatorRegex(mostUsedSpecial);
    for (const match of password.matchAll(isSeparator)) {
      if (match.index === void 0) continue;
      const i = match.index + 1;
      result.push({
        pattern: "separator",
        token: mostUsedSpecial,
        i,
        j: i
      });
    }
    return result;
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/Matching.esm.js
var Matching = class {
  constructor() {
    this.matchers = {
      date: MatchDate,
      dictionary: MatchDictionary,
      regex: MatchRegex,
      // @ts-ignore => TODO resolve this type issue. This is because it is possible to be async
      repeat: MatchRepeat,
      sequence: MatchSequence,
      spatial: MatchSpatial,
      separator: MatchSeparator
    };
  }
  match(password) {
    const matches = [];
    const promises = [];
    const matchers2 = [...Object.keys(this.matchers), ...Object.keys(zxcvbnOptions.matchers)];
    matchers2.forEach((key) => {
      if (!this.matchers[key] && !zxcvbnOptions.matchers[key]) {
        return;
      }
      const Matcher = this.matchers[key] ? this.matchers[key] : zxcvbnOptions.matchers[key].Matching;
      const usedMatcher = new Matcher();
      const result = usedMatcher.match({
        password,
        omniMatch: this
      });
      if (result instanceof Promise) {
        result.then((response) => {
          extend(matches, response);
        });
        promises.push(result);
      } else {
        extend(matches, result);
      }
    });
    if (promises.length > 0) {
      return new Promise((resolve, reject) => {
        Promise.all(promises).then(() => {
          resolve(sorted(matches));
        }).catch((error) => {
          reject(error);
        });
      });
    }
    return sorted(matches);
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/TimeEstimates.esm.js
var SECOND = 1;
var MINUTE = SECOND * 60;
var HOUR = MINUTE * 60;
var DAY = HOUR * 24;
var MONTH = DAY * 31;
var YEAR = MONTH * 12;
var CENTURY = YEAR * 100;
var times = {
  second: SECOND,
  minute: MINUTE,
  hour: HOUR,
  day: DAY,
  month: MONTH,
  year: YEAR,
  century: CENTURY
};
var TimeEstimates = class {
  translate(displayStr, value) {
    let key = displayStr;
    if (value !== void 0 && value !== 1) {
      key += "s";
    }
    const {
      timeEstimation
    } = zxcvbnOptions.translations;
    return timeEstimation[key].replace("{base}", `${value}`);
  }
  estimateAttackTimes(guesses) {
    const crackTimesSeconds = {
      onlineThrottling100PerHour: guesses / (100 / 3600),
      onlineNoThrottling10PerSecond: guesses / 10,
      offlineSlowHashing1e4PerSecond: guesses / 1e4,
      offlineFastHashing1e10PerSecond: guesses / 1e10
    };
    const crackTimesDisplay = {
      onlineThrottling100PerHour: "",
      onlineNoThrottling10PerSecond: "",
      offlineSlowHashing1e4PerSecond: "",
      offlineFastHashing1e10PerSecond: ""
    };
    Object.keys(crackTimesSeconds).forEach((scenario) => {
      const seconds = crackTimesSeconds[scenario];
      crackTimesDisplay[scenario] = this.displayTime(seconds);
    });
    return {
      crackTimesSeconds,
      crackTimesDisplay,
      score: this.guessesToScore(guesses)
    };
  }
  guessesToScore(guesses) {
    const DELTA = 5;
    if (guesses < 1e3 + DELTA) {
      return 0;
    }
    if (guesses < 1e6 + DELTA) {
      return 1;
    }
    if (guesses < 1e8 + DELTA) {
      return 2;
    }
    if (guesses < 1e10 + DELTA) {
      return 3;
    }
    return 4;
  }
  displayTime(seconds) {
    let displayStr = "centuries";
    let base;
    const timeKeys = Object.keys(times);
    const foundIndex = timeKeys.findIndex((time2) => seconds < times[time2]);
    if (foundIndex > -1) {
      displayStr = timeKeys[foundIndex - 1];
      if (foundIndex !== 0) {
        base = Math.round(seconds / times[displayStr]);
      } else {
        displayStr = "ltSecond";
      }
    }
    return this.translate(displayStr, base);
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/bruteforce/feedback.esm.js
var bruteforceMatcher2 = () => {
  return null;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/date/feedback.esm.js
var dateMatcher2 = () => {
  return {
    warning: zxcvbnOptions.translations.warnings.dates,
    suggestions: [zxcvbnOptions.translations.suggestions.dates]
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/dictionary/feedback.esm.js
var getDictionaryWarningPassword = (match, isSoleMatch) => {
  let warning = null;
  if (isSoleMatch && !match.l33t && !match.reversed) {
    if (match.rank <= 10) {
      warning = zxcvbnOptions.translations.warnings.topTen;
    } else if (match.rank <= 100) {
      warning = zxcvbnOptions.translations.warnings.topHundred;
    } else {
      warning = zxcvbnOptions.translations.warnings.common;
    }
  } else if (match.guessesLog10 <= 4) {
    warning = zxcvbnOptions.translations.warnings.similarToCommon;
  }
  return warning;
};
var getDictionaryWarningWikipedia = (match, isSoleMatch) => {
  let warning = null;
  if (isSoleMatch) {
    warning = zxcvbnOptions.translations.warnings.wordByItself;
  }
  return warning;
};
var getDictionaryWarningNames = (match, isSoleMatch) => {
  if (isSoleMatch) {
    return zxcvbnOptions.translations.warnings.namesByThemselves;
  }
  return zxcvbnOptions.translations.warnings.commonNames;
};
var getDictionaryWarning = (match, isSoleMatch) => {
  let warning = null;
  const dictName = match.dictionaryName;
  const isAName = dictName === "lastnames" || dictName.toLowerCase().includes("firstnames");
  if (dictName === "passwords") {
    warning = getDictionaryWarningPassword(match, isSoleMatch);
  } else if (dictName.includes("wikipedia")) {
    warning = getDictionaryWarningWikipedia(match, isSoleMatch);
  } else if (isAName) {
    warning = getDictionaryWarningNames(match, isSoleMatch);
  } else if (dictName === "userInputs") {
    warning = zxcvbnOptions.translations.warnings.userInputs;
  }
  return warning;
};
var dictionaryMatcher2 = (match, isSoleMatch) => {
  const warning = getDictionaryWarning(match, isSoleMatch);
  const suggestions = [];
  const word = match.token;
  if (word.match(START_UPPER)) {
    suggestions.push(zxcvbnOptions.translations.suggestions.capitalization);
  } else if (word.match(ALL_UPPER_INVERTED) && word.toLowerCase() !== word) {
    suggestions.push(zxcvbnOptions.translations.suggestions.allUppercase);
  }
  if (match.reversed && match.token.length >= 4) {
    suggestions.push(zxcvbnOptions.translations.suggestions.reverseWords);
  }
  if (match.l33t) {
    suggestions.push(zxcvbnOptions.translations.suggestions.l33t);
  }
  return {
    warning,
    suggestions
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/regex/feedback.esm.js
var regexMatcher2 = (match) => {
  if (match.regexName === "recentYear") {
    return {
      warning: zxcvbnOptions.translations.warnings.recentYears,
      suggestions: [zxcvbnOptions.translations.suggestions.recentYears, zxcvbnOptions.translations.suggestions.associatedYears]
    };
  }
  return {
    warning: null,
    suggestions: []
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/repeat/feedback.esm.js
var repeatMatcher2 = (match) => {
  let warning = zxcvbnOptions.translations.warnings.extendedRepeat;
  if (match.baseToken.length === 1) {
    warning = zxcvbnOptions.translations.warnings.simpleRepeat;
  }
  return {
    warning,
    suggestions: [zxcvbnOptions.translations.suggestions.repeated]
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/sequence/feedback.esm.js
var sequenceMatcher2 = () => {
  return {
    warning: zxcvbnOptions.translations.warnings.sequences,
    suggestions: [zxcvbnOptions.translations.suggestions.sequences]
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/spatial/feedback.esm.js
var spatialMatcher2 = (match) => {
  let warning = zxcvbnOptions.translations.warnings.keyPattern;
  if (match.turns === 1) {
    warning = zxcvbnOptions.translations.warnings.straightRow;
  }
  return {
    warning,
    suggestions: [zxcvbnOptions.translations.suggestions.longerKeyboardPattern]
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/matcher/separator/feedback.esm.js
var separatorMatcher2 = () => {
  return null;
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/Feedback.esm.js
var defaultFeedback = {
  warning: null,
  suggestions: []
};
var Feedback = class {
  constructor() {
    this.matchers = {
      bruteforce: bruteforceMatcher2,
      date: dateMatcher2,
      dictionary: dictionaryMatcher2,
      regex: regexMatcher2,
      repeat: repeatMatcher2,
      sequence: sequenceMatcher2,
      spatial: spatialMatcher2,
      separator: separatorMatcher2
    };
    this.defaultFeedback = {
      warning: null,
      suggestions: []
    };
    this.setDefaultSuggestions();
  }
  setDefaultSuggestions() {
    this.defaultFeedback.suggestions.push(zxcvbnOptions.translations.suggestions.useWords, zxcvbnOptions.translations.suggestions.noNeed);
  }
  getFeedback(score, sequence) {
    if (sequence.length === 0) {
      return this.defaultFeedback;
    }
    if (score > 2) {
      return defaultFeedback;
    }
    const extraFeedback = zxcvbnOptions.translations.suggestions.anotherWord;
    const longestMatch = this.getLongestMatch(sequence);
    let feedback = this.getMatchFeedback(longestMatch, sequence.length === 1);
    if (feedback !== null && feedback !== void 0) {
      feedback.suggestions.unshift(extraFeedback);
    } else {
      feedback = {
        warning: null,
        suggestions: [extraFeedback]
      };
    }
    return feedback;
  }
  getLongestMatch(sequence) {
    let longestMatch = sequence[0];
    const slicedSequence = sequence.slice(1);
    slicedSequence.forEach((match) => {
      if (match.token.length > longestMatch.token.length) {
        longestMatch = match;
      }
    });
    return longestMatch;
  }
  getMatchFeedback(match, isSoleMatch) {
    if (this.matchers[match.pattern]) {
      return this.matchers[match.pattern](match, isSoleMatch);
    }
    if (zxcvbnOptions.matchers[match.pattern] && "feedback" in zxcvbnOptions.matchers[match.pattern]) {
      return zxcvbnOptions.matchers[match.pattern].feedback(match, isSoleMatch);
    }
    return defaultFeedback;
  }
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/debounce.esm.js
var debounce = (func, wait, isImmediate) => {
  let timeout;
  return function debounce2(...args) {
    const context = this;
    const later = () => {
      timeout = void 0;
      if (!isImmediate) {
        func.apply(context, args);
      }
    };
    const shouldCallNow = isImmediate && !timeout;
    if (timeout !== void 0) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
    if (shouldCallNow) {
      return func.apply(context, args);
    }
    return void 0;
  };
};

// ../node_modules/.pnpm/@zxcvbn-ts+core@3.0.4/node_modules/@zxcvbn-ts/core/dist/index.esm.js
var time = () => (/* @__PURE__ */ new Date()).getTime();
var createReturnValue = (resolvedMatches, password, start) => {
  const feedback = new Feedback();
  const timeEstimates = new TimeEstimates();
  const matchSequence = scoring.mostGuessableMatchSequence(password, resolvedMatches);
  const calcTime = time() - start;
  const attackTimes = timeEstimates.estimateAttackTimes(matchSequence.guesses);
  return {
    calcTime,
    ...matchSequence,
    ...attackTimes,
    feedback: feedback.getFeedback(attackTimes.score, matchSequence.sequence)
  };
};
var main = (password, userInputs) => {
  if (userInputs) {
    zxcvbnOptions.extendUserInputsDictionary(userInputs);
  }
  const matching = new Matching();
  return matching.match(password);
};
var zxcvbn = (password, userInputs) => {
  const start = time();
  const matches = main(password, userInputs);
  if (matches instanceof Promise) {
    throw new Error("You are using a Promised matcher, please use `zxcvbnAsync` for it.");
  }
  return createReturnValue(matches, password, start);
};
var zxcvbnAsync = async (password, userInputs) => {
  const usedPassword = password.substring(0, zxcvbnOptions.maxLength);
  const start = time();
  const matches = await main(usedPassword, userInputs);
  return createReturnValue(matches, usedPassword, start);
};
export {
  Options,
  debounce,
  zxcvbn,
  zxcvbnAsync,
  zxcvbnOptions
};
//# sourceMappingURL=@zxcvbn-ts_core.js.map

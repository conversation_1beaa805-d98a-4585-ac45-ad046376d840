{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/micromark-util-sanitize-uri@2.0.1/node_modules/micromark-util-sanitize-uri/dev/index.js"], "sourcesContent": ["import {asciiAlphanumeric} from 'micromark-util-character'\nimport {encode} from 'micromark-util-encode'\nimport {codes, values} from 'micromark-util-symbol'\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | null | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''))\n\n  if (!protocol) {\n    return value\n  }\n\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === codes.percentSign &&\n      asciiAlphanumeric(value.charCodeAt(index + 1)) &&\n      asciiAlphanumeric(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55_295 && code < 57_344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56_320 && next > 56_319 && next < 57_344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = values.replacementCharacter\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n\n  return result.join('') + value.slice(start)\n}\n"], "mappings": ";;;;;;;AAiEO,SAAS,aAAa,OAAO;AAElC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,UAAM,OAAO,MAAM,WAAW,KAAK;AAEnC,QAAI,UAAU;AAGd,QACE,SAAS,MAAM,eACf,kBAAkB,MAAM,WAAW,QAAQ,CAAC,CAAC,KAC7C,kBAAkB,MAAM,WAAW,QAAQ,CAAC,CAAC,GAC7C;AACA,aAAO;AAAA,IACT,WAES,OAAO,KAAK;AACnB,UAAI,CAAC,oBAAoB,KAAK,OAAO,aAAa,IAAI,CAAC,GAAG;AACxD,kBAAU,OAAO,aAAa,IAAI;AAAA,MACpC;AAAA,IACF,WAES,OAAO,SAAU,OAAO,OAAQ;AACvC,YAAM,OAAO,MAAM,WAAW,QAAQ,CAAC;AAGvC,UAAI,OAAO,SAAU,OAAO,SAAU,OAAO,OAAQ;AACnD,kBAAU,OAAO,aAAa,MAAM,IAAI;AACxC,eAAO;AAAA,MACT,OAEK;AACH,kBAAU,OAAO;AAAA,MACnB;AAAA,IACF,OAEK;AACH,gBAAU,OAAO,aAAa,IAAI;AAAA,IACpC;AAEA,QAAI,SAAS;AACX,aAAO,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,mBAAmB,OAAO,CAAC;AAClE,cAAQ,QAAQ,OAAO;AACvB,gBAAU;AAAA,IACZ;AAEA,QAAI,MAAM;AACR,eAAS;AACT,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,OAAO,KAAK,EAAE,IAAI,MAAM,MAAM,KAAK;AAC5C;", "names": []}
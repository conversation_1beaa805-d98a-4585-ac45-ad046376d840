{"version": 3, "sources": ["../../../../../../node_modules/.pnpm/country-codes-list@2.0.0/node_modules/country-codes-list/dist/utils/groupBy.js", "../../../../../../node_modules/.pnpm/country-codes-list@2.0.0/node_modules/country-codes-list/dist/utils/supplant.js", "../../../../../../node_modules/.pnpm/country-codes-list@2.0.0/node_modules/country-codes-list/dist/countriesData.js", "../../../../../../node_modules/.pnpm/country-codes-list@2.0.0/node_modules/country-codes-list/dist/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * Groups an array by a specified key.\n * @param array - The array to group.\n * @param key - The key to group the array by.\n * @returns An object where each key is a unique value from the array, and each value is an array of items from the original array that match the key.\n */\nfunction groupBy(array, key) {\n    return array.reduce((result, item) => {\n        const groupKey = String(item[key]);\n        if (!result[groupKey]) {\n            result[groupKey] = [];\n        }\n        result[groupKey].push(item);\n        return result;\n    }, {});\n}\nexports.default = groupBy;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * Replaces placeholders in a string with values from an object.\n * @param template - The string containing placeholders.\n * @param data - The object containing key-value pairs for replacement.\n * @returns The string with placeholders replaced by values.\n */\nfunction supplant(template, data) {\n    return template.replace(/{([^{}]*)}/g, (match, key) => {\n        const value = data[key];\n        return typeof value === \"string\" || typeof value === \"number\"\n            ? value.toString()\n            : match;\n    });\n}\nexports.default = supplant;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst countriesData = [\n    {\n        countryNameEn: \"Andorra\",\n        countryNameLocal: \"Andorra\",\n        countryCode: \"AD\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ca\",\n        officialLanguageNameEn: \"Catalan, Valencian\",\n        officialLanguageNameLocal: \"Català, Valencià\",\n        countryCallingCode: \"376\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇦🇩\",\n    },\n    {\n        countryNameEn: \"Afghanistan\",\n        countryNameLocal: \"د افغانستان اسلامي دولتدولت اسلامی افغانستان, جمهوری اسلامی افغانستان\",\n        countryCode: \"AF\",\n        currencyCode: \"AFN\",\n        currencyNameEn: \"Afghan afghani\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fa\",\n        officialLanguageNameEn: \"Persian\",\n        officialLanguageNameLocal: \"فارسی\",\n        countryCallingCode: \"93\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇦🇫\",\n    },\n    {\n        countryNameEn: \"Antigua and Barbuda\",\n        countryNameLocal: \"Antigua and Barbuda\",\n        countryCode: \"AG\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1268\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇦🇬\",\n    },\n    {\n        countryNameEn: \"Anguilla\",\n        countryNameLocal: \"Anguilla\",\n        countryCode: \"AI\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1264\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇦🇮\",\n    },\n    {\n        countryNameEn: \"Albania\",\n        countryNameLocal: \"Shqipëria\",\n        countryCode: \"AL\",\n        currencyCode: \"ALL\",\n        currencyNameEn: \"Albanian lek\",\n        tinType: \"NIPT\",\n        tinName: \"Numri i Identifikimit për Personin e Tatueshëm\",\n        officialLanguageCode: \"sq\",\n        officialLanguageNameEn: \"Albanian\",\n        officialLanguageNameLocal: \"Shqip\",\n        countryCallingCode: \"355\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇦🇱\",\n    },\n    {\n        countryNameEn: \"Armenia\",\n        countryNameLocal: \"Հայաստան\",\n        countryCode: \"AM\",\n        currencyCode: \"AMD\",\n        currencyNameEn: \"Armenian dram\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"hy\",\n        officialLanguageNameEn: \"Armenian\",\n        officialLanguageNameLocal: \"Հայերեն\",\n        countryCallingCode: \"374\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇦🇲\",\n    },\n    {\n        countryNameEn: \"Angola\",\n        countryNameLocal: \"Angola\",\n        countryCode: \"AO\",\n        currencyCode: \"AOA\",\n        currencyNameEn: \"Angolan kwanza\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"244\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇦🇴\",\n    },\n    {\n        countryNameEn: \"Antarctica\",\n        countryNameLocal: \"Antarctica, Antártico, Antarctique, Антарктике\",\n        countryCode: \"AQ\",\n        currencyCode: \"\",\n        currencyNameEn: \"\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"672\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇦🇶\",\n    },\n    {\n        countryNameEn: \"Argentina\",\n        countryNameLocal: \"Argentina\",\n        countryCode: \"AR\",\n        currencyCode: \"ARS\",\n        currencyNameEn: \"Argentine peso\",\n        tinType: \"CUIT\",\n        tinName: \"Código Único de Identificación Tributaria\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"54\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇦🇷\",\n    },\n    {\n        countryNameEn: \"American Samoa\",\n        countryNameLocal: \"American Samoa\",\n        countryCode: \"AS\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1684\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇦🇸\",\n    },\n    {\n        countryNameEn: \"Austria\",\n        countryNameLocal: \"Österreich\",\n        countryCode: \"AT\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"UID\",\n        tinName: \"Umsatzsteuer-Identifikationsnummer\",\n        officialLanguageCode: \"de\",\n        officialLanguageNameEn: \"German\",\n        officialLanguageNameLocal: \"Deutsch\",\n        countryCallingCode: \"43\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇦🇹\",\n    },\n    {\n        countryNameEn: \"Australia\",\n        countryNameLocal: \"Australia\",\n        countryCode: \"AU\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"ABN\",\n        tinName: \"Australian Business Number\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"61\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇦🇺\",\n    },\n    {\n        countryNameEn: \"Aruba\",\n        countryNameLocal: \"Aruba\",\n        countryCode: \"AW\",\n        currencyCode: \"AWG\",\n        currencyNameEn: \"Aruban florin\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"297\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇦🇼\",\n    },\n    {\n        countryNameEn: \"Åland Islands\",\n        countryNameLocal: \"Åland\",\n        countryCode: \"AX\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"sv\",\n        officialLanguageNameEn: \"Swedish\",\n        officialLanguageNameLocal: \"Svenska\",\n        countryCallingCode: \"358\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇦🇽\",\n    },\n    {\n        countryNameEn: \"Azerbaijan\",\n        countryNameLocal: \"Azərbaycan\",\n        countryCode: \"AZ\",\n        currencyCode: \"AZN\",\n        currencyNameEn: \"Azerbaijani manat\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"az\",\n        officialLanguageNameEn: \"Azerbaijani\",\n        officialLanguageNameLocal: \"azərbaycan dili\",\n        countryCallingCode: \"994\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇦🇿\",\n    },\n    {\n        countryNameEn: \"Bosnia and Herzegovina\",\n        countryNameLocal: \"Bosna i Hercegovina\",\n        countryCode: \"BA\",\n        currencyCode: \"BAM\",\n        currencyNameEn: \"Bosnia and Herzegovina convertible mark\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"bs\",\n        officialLanguageNameEn: \"Bosnian\",\n        officialLanguageNameLocal: \"bosanski jezik\",\n        countryCallingCode: \"387\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇧🇦\",\n    },\n    {\n        countryNameEn: \"Barbados\",\n        countryNameLocal: \"Barbados\",\n        countryCode: \"BB\",\n        currencyCode: \"BBD\",\n        currencyNameEn: \"Barbados dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1246\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇧🇧\",\n    },\n    {\n        countryNameEn: \"Bangladesh\",\n        countryNameLocal: \"গণপ্রজাতন্ত্রী বাংলাদেশ\",\n        countryCode: \"BD\",\n        currencyCode: \"BDT\",\n        currencyNameEn: \"Bangladeshi taka\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"bn\",\n        officialLanguageNameEn: \"Bengali\",\n        officialLanguageNameLocal: \"বাংলা\",\n        countryCallingCode: \"880\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇧🇩\",\n    },\n    {\n        countryNameEn: \"Belgium\",\n        countryNameLocal: \"België, Belgique, Belgien\",\n        countryCode: \"BE\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"n° TVABTW-nr Mwst-nr\",\n        tinName: \"BTW identificatienummer / Numéro de TVA\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"32\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇧🇪\",\n    },\n    {\n        countryNameEn: \"Burkina Faso\",\n        countryNameLocal: \"Burkina Faso\",\n        countryCode: \"BF\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"226\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇧🇫\",\n    },\n    {\n        countryNameEn: \"Bulgaria\",\n        countryNameLocal: \"България\",\n        countryCode: \"BG\",\n        currencyCode: \"BGN\",\n        currencyNameEn: \"Bulgarian lev\",\n        tinType: \"ДДС номер\",\n        tinName: \"Идентификационен номер по ДДС\",\n        officialLanguageCode: \"bg\",\n        officialLanguageNameEn: \"Bulgarian\",\n        officialLanguageNameLocal: \"български език\",\n        countryCallingCode: \"359\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇧🇬\",\n    },\n    {\n        countryNameEn: \"Bahrain\",\n        countryNameLocal: \"البحرين\",\n        countryCode: \"BH\",\n        currencyCode: \"BHD\",\n        currencyNameEn: \"Bahraini dinar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"973\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇧🇭\",\n    },\n    {\n        countryNameEn: \"Burundi\",\n        countryNameLocal: \"Burundi\",\n        countryCode: \"BI\",\n        currencyCode: \"BIF\",\n        currencyNameEn: \"Burundian franc\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"257\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇧🇮\",\n    },\n    {\n        countryNameEn: \"Benin\",\n        countryNameLocal: \"Bénin\",\n        countryCode: \"BJ\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"229\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇧🇯\",\n    },\n    {\n        countryNameEn: \"Saint Barthélemy\",\n        countryNameLocal: \"Saint-Barthélemy\",\n        countryCode: \"BL\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"590\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇧🇱\",\n    },\n    {\n        countryNameEn: \"Bermuda\",\n        countryNameLocal: \"Bermuda\",\n        countryCode: \"BM\",\n        currencyCode: \"BMD\",\n        currencyNameEn: \"Bermudian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1441\",\n        areaCodes: [],\n        region: \"North America\",\n        flag: \"🇧🇲\",\n    },\n    {\n        countryNameEn: \"Brunei Darussalam\",\n        countryNameLocal: \"Brunei Darussalam\",\n        countryCode: \"BN\",\n        currencyCode: \"BND\",\n        currencyNameEn: \"Brunei dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ms\",\n        officialLanguageNameEn: \"Malay\",\n        officialLanguageNameLocal: \"Bahasa Melayu, بهاس ملايو‎\",\n        countryCallingCode: \"673\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇧🇳\",\n    },\n    {\n        countryNameEn: \"Bolivia (Plurinational State of)\",\n        countryNameLocal: \"Bolivia, Bulibiya, Volívia, Wuliwya\",\n        countryCode: \"BO\",\n        currencyCode: \"BOP\",\n        currencyNameEn: \"\",\n        tinType: \"NIT\",\n        tinName: \"Número de Identificación Tributaria\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"591\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇧🇴\",\n    },\n    {\n        countryNameEn: \"Bonaire, Sint Eustatius and Saba\",\n        countryNameLocal: \"Caribisch Nederland\",\n        countryCode: \"BQ\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"5997\",\n        areaCodes: [],\n        region: \"Unknown\",\n        flag: \"🇧🇶\",\n    },\n    {\n        countryNameEn: \"Brazil\",\n        countryNameLocal: \"Brasil\",\n        countryCode: \"BR\",\n        currencyCode: \"BRL\",\n        currencyNameEn: \"Brazilian real\",\n        tinType: \"CNPJ\",\n        tinName: \"Cadastro Nacional de Pessoa Jurídica\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"55\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇧🇷\",\n    },\n    {\n        countryNameEn: \"Bhutan\",\n        countryNameLocal: \"འབྲུག་ཡུལ\",\n        countryCode: \"BT\",\n        currencyCode: \"BTN\",\n        currencyNameEn: \"Bhutanese ngultrum\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"dz\",\n        officialLanguageNameEn: \"Dzongkha\",\n        officialLanguageNameLocal: \"རྫོང་ཁ\",\n        countryCallingCode: \"975\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇧🇹\",\n    },\n    {\n        countryNameEn: \"Bouvet Island\",\n        countryNameLocal: \"Bouvetøya\",\n        countryCode: \"BV\",\n        currencyCode: \"NOK\",\n        currencyNameEn: \"Norwegian krone\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"no\",\n        officialLanguageNameEn: \"Norwegian\",\n        officialLanguageNameLocal: \"Norsk\",\n        countryCallingCode: \"47\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇧🇻\",\n    },\n    {\n        countryNameEn: \"Botswana\",\n        countryNameLocal: \"Botswana\",\n        countryCode: \"BW\",\n        currencyCode: \"BWP\",\n        currencyNameEn: \"Botswana pula\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"267\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇧🇼\",\n    },\n    {\n        countryNameEn: \"Belarus\",\n        countryNameLocal: \"Беларусь\",\n        countryCode: \"BY\",\n        currencyCode: \"BYR\",\n        currencyNameEn: \"\",\n        tinType: \"УНП (UNP)\",\n        tinName: \"Учетный номер плательщика\",\n        officialLanguageCode: \"be\",\n        officialLanguageNameEn: \"Belarusian\",\n        officialLanguageNameLocal: \"беларуская мова\",\n        countryCallingCode: \"375\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇧🇾\",\n    },\n    {\n        countryNameEn: \"Belize\",\n        countryNameLocal: \"Belize\",\n        countryCode: \"BZ\",\n        currencyCode: \"BZD\",\n        currencyNameEn: \"Belize dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"501\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇧🇿\",\n    },\n    {\n        countryNameEn: \"Canada\",\n        countryNameLocal: \"Canada\",\n        countryCode: \"CA\",\n        currencyCode: \"CAD\",\n        currencyNameEn: \"Canadian dollar\",\n        tinType: \"BN / NE\",\n        tinName: \"Business Number\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1\",\n        areaCodes: [\n            \"403\",\n            \"587\",\n            \"780\",\n            \"825\",\n            \"236\",\n            \"250\",\n            \"604\",\n            \"672\",\n            \"778\",\n            \"204\",\n            \"431\",\n            \"506\",\n            \"709\",\n            \"782\",\n            \"902\",\n            \"226\",\n            \"249\",\n            \"289\",\n            \"343\",\n            \"365\",\n            \"416\",\n            \"437\",\n            \"519\",\n            \"548\",\n            \"613\",\n            \"647\",\n            \"705\",\n            \"807\",\n            \"905\",\n            \"367\",\n            \"418\",\n            \"438\",\n            \"450\",\n            \"514\",\n            \"579\",\n            \"581\",\n            \"819\",\n            \"873\",\n            \"306\",\n            \"639\",\n            \"867\",\n        ],\n        region: \"North America\",\n        flag: \"🇨🇦\",\n    },\n    {\n        countryNameEn: \"Switzerland\",\n        countryNameLocal: \"Schweiz, Suisse, Svizzera, Svizra\",\n        countryCode: \"CH\",\n        currencyCode: \"CHF\",\n        currencyNameEn: \"Swiss franc\",\n        tinType: \"MWST/TVA/IVA\",\n        tinName: \"Mehrwertsteuernummer\",\n        officialLanguageCode: \"de\",\n        officialLanguageNameEn: \"German\",\n        officialLanguageNameLocal: \"Deutsch\",\n        countryCallingCode: \"41\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇨🇭\",\n    },\n    {\n        countryNameEn: \"Côte d'Ivoire\",\n        countryNameLocal: \"Côte d'Ivoire\",\n        countryCode: \"CI\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"225\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇨🇮\",\n    },\n    {\n        countryNameEn: \"Chile\",\n        countryNameLocal: \"Chile\",\n        countryCode: \"CL\",\n        currencyCode: \"CLP\",\n        currencyNameEn: \"Chilean peso\",\n        tinType: \"RUT\",\n        tinName: \"Rol Único Tributario\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"56\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇨🇱\",\n    },\n    {\n        countryNameEn: \"Cameroon\",\n        countryNameLocal: \"Cameroun, Cameroon\",\n        countryCode: \"CM\",\n        currencyCode: \"XAF\",\n        currencyNameEn: \"CFA franc BEAC\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"237\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇨🇲\",\n    },\n    {\n        countryNameEn: \"China\",\n        countryNameLocal: \"中国\",\n        countryCode: \"CN\",\n        currencyCode: \"CNY\",\n        currencyNameEn: \"Renminbi (Chinese) yuan\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"zh-hans\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"86\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇨🇳\",\n    },\n    {\n        countryNameEn: \"Colombia\",\n        countryNameLocal: \"Colombia\",\n        countryCode: \"CO\",\n        currencyCode: \"COP\",\n        currencyNameEn: \"Colombian peso\",\n        tinType: \"NIT\",\n        tinName: \"Número De Identificación Tributaria\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"57\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇨🇴\",\n    },\n    {\n        countryNameEn: \"Costa Rica\",\n        countryNameLocal: \"Costa Rica\",\n        countryCode: \"CR\",\n        currencyCode: \"CRC\",\n        currencyNameEn: \"Costa Rican colon\",\n        tinType: \"\",\n        tinName: \"Cédula Jurídica\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"506\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇨🇷\",\n    },\n    {\n        countryNameEn: \"Cuba\",\n        countryNameLocal: \"Cuba\",\n        countryCode: \"CU\",\n        currencyCode: \"CUC\",\n        currencyNameEn: \"Cuban convertible peso\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"53\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇨🇺\",\n    },\n    {\n        countryNameEn: \"Cabo Verde\",\n        countryNameLocal: \"Cabo Verde\",\n        countryCode: \"CV\",\n        currencyCode: \"CVE\",\n        currencyNameEn: \"Cape Verdean escudo\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"238\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇨🇻\",\n    },\n    {\n        countryNameEn: \"Curaçao\",\n        countryNameLocal: \"Curaçao\",\n        countryCode: \"CW\",\n        currencyCode: \"ANG\",\n        currencyNameEn: \"Netherlands Antillean guilder\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"599\",\n        areaCodes: [],\n        region: \"Unknown\",\n        flag: \"🇨🇼\",\n    },\n    {\n        countryNameEn: \"Christmas Island\",\n        countryNameLocal: \"Christmas Island\",\n        countryCode: \"CX\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"61\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇨🇽\",\n    },\n    {\n        countryNameEn: \"Cyprus\",\n        countryNameLocal: \"Κύπρος, Kibris\",\n        countryCode: \"CY\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"ΦΠΑ\",\n        tinName: \"Αριθμός Εγγραφής Φ.Π.Α.\",\n        officialLanguageCode: \"el\",\n        officialLanguageNameEn: \"Greek, Modern (1453-)\",\n        officialLanguageNameLocal: \"ελληνικά\",\n        countryCallingCode: \"357\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇨🇾\",\n    },\n    {\n        countryNameEn: \"Germany\",\n        countryNameLocal: \"Deutschland\",\n        countryCode: \"DE\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"USt-IdNr.\",\n        tinName: \"Umsatzsteuer-Identifikationsnummer\",\n        officialLanguageCode: \"de\",\n        officialLanguageNameEn: \"German\",\n        officialLanguageNameLocal: \"Deutsch\",\n        countryCallingCode: \"49\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇩🇪\",\n    },\n    {\n        countryNameEn: \"Djibouti\",\n        countryNameLocal: \"Djibouti, جيبوتي, Jabuuti, Gabuutih\",\n        countryCode: \"DJ\",\n        currencyCode: \"DJF\",\n        currencyNameEn: \"Djiboutian franc\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"253\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇩🇯\",\n    },\n    {\n        countryNameEn: \"Denmark\",\n        countryNameLocal: \"Danmark\",\n        countryCode: \"DK\",\n        currencyCode: \"DKK\",\n        currencyNameEn: \"Danish krone\",\n        tinType: \"CVR\",\n        tinName: \"Momsregistreringsnummer\",\n        officialLanguageCode: \"da\",\n        officialLanguageNameEn: \"Danish\",\n        officialLanguageNameLocal: \"dansk\",\n        countryCallingCode: \"45\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇩🇰\",\n    },\n    {\n        countryNameEn: \"Dominica\",\n        countryNameLocal: \"Dominica\",\n        countryCode: \"DM\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"767\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇩🇲\",\n    },\n    {\n        countryNameEn: \"Algeria\",\n        countryNameLocal: \"الجزائر\",\n        countryCode: \"DZ\",\n        currencyCode: \"DZD\",\n        currencyNameEn: \"Algerian dinar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"213\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇩🇿\",\n    },\n    {\n        countryNameEn: \"Ecuador\",\n        countryNameLocal: \"Ecuador\",\n        countryCode: \"EC\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"RUC\",\n        tinName: \"Número de Registro Unico de Contribuyentes\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"593\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇪🇨\",\n    },\n    {\n        countryNameEn: \"Estonia\",\n        countryNameLocal: \"Eesti\",\n        countryCode: \"EE\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"KMKR\",\n        tinName: \"Käibemaksukohustuslase number\",\n        officialLanguageCode: \"et\",\n        officialLanguageNameEn: \"Estonian\",\n        officialLanguageNameLocal: \"eesti, eesti keel\",\n        countryCallingCode: \"372\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇪🇪\",\n    },\n    {\n        countryNameEn: \"Egypt\",\n        countryNameLocal: \"مصر\",\n        countryCode: \"EG\",\n        currencyCode: \"EGP\",\n        currencyNameEn: \"Egyptian pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"20\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇪🇬\",\n    },\n    {\n        countryNameEn: \"Western Sahara\",\n        countryNameLocal: \"Sahara Occidental\",\n        countryCode: \"EH\",\n        currencyCode: \"MAD\",\n        currencyNameEn: \"Moroccan dirham\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"212\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇪🇭\",\n    },\n    {\n        countryNameEn: \"Eritrea\",\n        countryNameLocal: \"ኤርትራ, إرتريا, Eritrea\",\n        countryCode: \"ER\",\n        currencyCode: \"ERN\",\n        currencyNameEn: \"Eritrean nakfa\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ti\",\n        officialLanguageNameEn: \"Tigrinya\",\n        officialLanguageNameLocal: \"ትግርኛ\",\n        countryCallingCode: \"291\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇪🇷\",\n    },\n    {\n        countryNameEn: \"Spain\",\n        countryNameLocal: \"España\",\n        countryCode: \"ES\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"NIF (CIF)\",\n        tinName: \"Número de Identificación Fiscal (formerly named Código de Identificación Fiscal)\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"34\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇪🇸\",\n    },\n    {\n        countryNameEn: \"Ethiopia\",\n        countryNameLocal: \"ኢትዮጵያ, Itoophiyaa\",\n        countryCode: \"ET\",\n        currencyCode: \"ETB\",\n        currencyNameEn: \"Ethiopian birr\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"am\",\n        officialLanguageNameEn: \"Amharic\",\n        officialLanguageNameLocal: \"አማርኛ\",\n        countryCallingCode: \"251\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇪🇹\",\n    },\n    {\n        countryNameEn: \"Finland\",\n        countryNameLocal: \"Suomi\",\n        countryCode: \"FI\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"ALV nro\",\n        tinName: \"Arvonlisäveronumero\",\n        officialLanguageCode: \"fi\",\n        officialLanguageNameEn: \"Finnish\",\n        officialLanguageNameLocal: \"suomi, suomen kieli\",\n        countryCallingCode: \"358\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇫🇮\",\n    },\n    {\n        countryNameEn: \"Fiji\",\n        countryNameLocal: \"Fiji\",\n        countryCode: \"FJ\",\n        currencyCode: \"FJD\",\n        currencyNameEn: \"Fiji dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"679\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇫🇯\",\n    },\n    {\n        countryNameEn: \"Micronesia (Federated States of)\",\n        countryNameLocal: \"Micronesia\",\n        countryCode: \"FM\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"691\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇫🇲\",\n    },\n    {\n        countryNameEn: \"France\",\n        countryNameLocal: \"France\",\n        countryCode: \"FR\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"n° TVA\",\n        tinName: \"Numéro d'identification à la taxe sur la valeur ajoutée / Numéro de TVA intracommunautaire\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"33\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇫🇷\",\n    },\n    {\n        countryNameEn: \"Gabon\",\n        countryNameLocal: \"Gabon\",\n        countryCode: \"GA\",\n        currencyCode: \"XAF\",\n        currencyNameEn: \"CFA franc BEAC\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"241\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇬🇦\",\n    },\n    {\n        countryNameEn: \"Grenada\",\n        countryNameLocal: \"Grenada\",\n        countryCode: \"GD\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1473\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇬🇩\",\n    },\n    {\n        countryNameEn: \"Georgia\",\n        countryNameLocal: \"საქართველო\",\n        countryCode: \"GE\",\n        currencyCode: \"GEL\",\n        currencyNameEn: \"Georgian lari\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ka\",\n        officialLanguageNameEn: \"Georgian\",\n        officialLanguageNameLocal: \"ქართული\",\n        countryCallingCode: \"995\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇬🇪\",\n    },\n    {\n        countryNameEn: \"French Guiana\",\n        countryNameLocal: \"Guyane française\",\n        countryCode: \"GF\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"594\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇬🇫\",\n    },\n    {\n        countryNameEn: \"Guernsey\",\n        countryNameLocal: \"Guernsey\",\n        countryCode: \"GG\",\n        currencyCode: \"GBP\",\n        currencyNameEn: \"Pound sterling\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"44\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇬🇬\",\n    },\n    {\n        countryNameEn: \"Ghana\",\n        countryNameLocal: \"Ghana\",\n        countryCode: \"GH\",\n        currencyCode: \"GHS\",\n        currencyNameEn: \"Ghanaian cedi\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"233\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇬🇭\",\n    },\n    {\n        countryNameEn: \"Gibraltar\",\n        countryNameLocal: \"Gibraltar\",\n        countryCode: \"GI\",\n        currencyCode: \"GIP\",\n        currencyNameEn: \"Gibraltar pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"350\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇬🇮\",\n    },\n    {\n        countryNameEn: \"Greenland\",\n        countryNameLocal: \"Kalaallit Nunaat, Grønland\",\n        countryCode: \"GL\",\n        currencyCode: \"DKK\",\n        currencyNameEn: \"Danish krone\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"kl\",\n        officialLanguageNameEn: \"Kalaallisut, Greenlandic\",\n        officialLanguageNameLocal: \"kalaallisut, kalaallit oqaasii\",\n        countryCallingCode: \"299\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇬🇱\",\n    },\n    {\n        countryNameEn: \"Guinea\",\n        countryNameLocal: \"Guinée\",\n        countryCode: \"GN\",\n        currencyCode: \"GNF\",\n        currencyNameEn: \"Guinean franc\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"224\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇬🇳\",\n    },\n    {\n        countryNameEn: \"Guadeloupe\",\n        countryNameLocal: \"Guadeloupe\",\n        countryCode: \"GP\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"590\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇬🇵\",\n    },\n    {\n        countryNameEn: \"Equatorial Guinea\",\n        countryNameLocal: \"Guiena ecuatorial, Guinée équatoriale, Guiné Equatorial\",\n        countryCode: \"GQ\",\n        currencyCode: \"XAF\",\n        currencyNameEn: \"CFA franc BEAC\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"240\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇬🇶\",\n    },\n    {\n        countryNameEn: \"Greece\",\n        countryNameLocal: \"Ελλάδα\",\n        countryCode: \"GR\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"el\",\n        officialLanguageNameEn: \"Greek, Modern (1453-)\",\n        officialLanguageNameLocal: \"ελληνικά\",\n        countryCallingCode: \"30\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇬🇷\",\n    },\n    {\n        countryNameEn: \"South Georgia and the South Sandwich Islands\",\n        countryNameLocal: \"South Georgia and the South Sandwich Islands\",\n        countryCode: \"GS\",\n        currencyCode: \"\",\n        currencyNameEn: \"\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"500\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇬🇸\",\n    },\n    {\n        countryNameEn: \"Guatemala\",\n        countryNameLocal: \"Guatemala\",\n        countryCode: \"GT\",\n        currencyCode: \"GTQ\",\n        currencyNameEn: \"Guatemalan quetzal\",\n        tinType: \"NIT\",\n        tinName: \"Número de Identificación Tributaria\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"502\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇬🇹\",\n    },\n    {\n        countryNameEn: \"Guam\",\n        countryNameLocal: \"Guam, Guåhån\",\n        countryCode: \"GU\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇬🇺\",\n    },\n    {\n        countryNameEn: \"Guinea-Bissau\",\n        countryNameLocal: \"Guiné-Bissau\",\n        countryCode: \"GW\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"245\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇬🇼\",\n    },\n    {\n        countryNameEn: \"Guyana\",\n        countryNameLocal: \"Guyana\",\n        countryCode: \"GY\",\n        currencyCode: \"GYD\",\n        currencyNameEn: \"Guyanese dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"592\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇬🇾\",\n    },\n    {\n        countryNameEn: \"Hong Kong\",\n        countryNameLocal: \"香港, Hong Kong\",\n        countryCode: \"HK\",\n        currencyCode: \"HKD\",\n        currencyNameEn: \"Hong Kong dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"zh-hant\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"852\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇭🇰\",\n    },\n    {\n        countryNameEn: \"Honduras\",\n        countryNameLocal: \"Honduras\",\n        countryCode: \"HN\",\n        currencyCode: \"HNL\",\n        currencyNameEn: \"Honduran lempira\",\n        tinType: \"RTN\",\n        tinName: \"Registro Tributario Nacional\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"504\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇭🇳\",\n    },\n    {\n        countryNameEn: \"Croatia\",\n        countryNameLocal: \"Hrvatska\",\n        countryCode: \"HR\",\n        currencyCode: \"HRK\",\n        currencyNameEn: \"Croatian kuna\",\n        tinType: \"PDV-ID; OIB\",\n        tinName: \"PDV Id. Broj OIB\",\n        officialLanguageCode: \"hr\",\n        officialLanguageNameEn: \"Croatian\",\n        officialLanguageNameLocal: \"hrvatski jezik\",\n        countryCallingCode: \"385\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇭🇷\",\n    },\n    {\n        countryNameEn: \"Haiti\",\n        countryNameLocal: \"Haïti, Ayiti\",\n        countryCode: \"HT\",\n        currencyCode: \"HTG\",\n        currencyNameEn: \"Haitian gourde\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"509\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇭🇹\",\n    },\n    {\n        countryNameEn: \"Hungary\",\n        countryNameLocal: \"Magyarország\",\n        countryCode: \"HU\",\n        currencyCode: \"HUF\",\n        currencyNameEn: \"Hungarian forint\",\n        tinType: \"ANUM\",\n        tinName: \"Közösségi adószám\",\n        officialLanguageCode: \"hu\",\n        officialLanguageNameEn: \"Hungarian\",\n        officialLanguageNameLocal: \"magyar\",\n        countryCallingCode: \"36\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇭🇺\",\n    },\n    {\n        countryNameEn: \"Indonesia\",\n        countryNameLocal: \"Indonesia\",\n        countryCode: \"ID\",\n        currencyCode: \"IDR\",\n        currencyNameEn: \"Indonesian rupiah\",\n        tinType: \"NPWP\",\n        tinName: \"Nomor Pokok Wajib Pajak\",\n        officialLanguageCode: \"id\",\n        officialLanguageNameEn: \"Indonesian\",\n        officialLanguageNameLocal: \"Bahasa Indonesia\",\n        countryCallingCode: \"62\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇮🇩\",\n    },\n    {\n        countryNameEn: \"Ireland\",\n        countryNameLocal: \"Ireland, Éire\",\n        countryCode: \"IE\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"VAT or CBL\",\n        tinName: \"Value added tax identification no.\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"353\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇮🇪\",\n    },\n    {\n        countryNameEn: \"Israel\",\n        countryNameLocal: \"ישראל\",\n        countryCode: \"IL\",\n        currencyCode: \"ILS\",\n        currencyNameEn: \"Israeli new shekel\",\n        tinType: \"\",\n        tinName: \"מס' עוסק מורשה / ח\\\"פ\",\n        officialLanguageCode: \"he\",\n        officialLanguageNameEn: \"Hebrew\",\n        officialLanguageNameLocal: \"עברית\",\n        countryCallingCode: \"972\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇮🇱\",\n    },\n    {\n        countryNameEn: \"Isle of Man\",\n        countryNameLocal: \"Isle of Man\",\n        countryCode: \"IM\",\n        currencyCode: \"GBP\",\n        currencyNameEn: \"Pound sterling\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"44\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇮🇲\",\n    },\n    {\n        countryNameEn: \"India\",\n        countryNameLocal: \"भारत, India\",\n        countryCode: \"IN\",\n        currencyCode: \"INR\",\n        currencyNameEn: \"Indian rupee\",\n        tinType: \"VAT TIN / CST TIN\",\n        tinName: \"Value Added Tax - Taxpayer Identification Number / Central Sales Tax - Taxpayer Identification Number (In most states)Not applicable\",\n        officialLanguageCode: \"hi\",\n        officialLanguageNameEn: \"Hindi\",\n        officialLanguageNameLocal: \"हिन्दी, हिंदी\",\n        countryCallingCode: \"91\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇮🇳\",\n    },\n    {\n        countryNameEn: \"British Indian Ocean Territories\",\n        countryNameLocal: \"British Indian Ocean Territories\",\n        countryCode: \"IO\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"246\",\n        areaCodes: [],\n        region: \"Indian Ocean\",\n        flag: \"🇮🇴\",\n    },\n    {\n        countryNameEn: \"Iraq\",\n        countryNameLocal: \"العراق, Iraq\",\n        countryCode: \"IQ\",\n        currencyCode: \"IQD\",\n        currencyNameEn: \"Iraqi dinar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"964\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇮🇶\",\n    },\n    {\n        countryNameEn: \"Iran (Islamic Republic of)\",\n        countryNameLocal: \"ایران\",\n        countryCode: \"IR\",\n        currencyCode: \"IRR\",\n        currencyNameEn: \"Iranian rial\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fa\",\n        officialLanguageNameEn: \"Persian\",\n        officialLanguageNameLocal: \"فارسی\",\n        countryCallingCode: \"98\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇮🇷\",\n    },\n    {\n        countryNameEn: \"Iceland\",\n        countryNameLocal: \"Ísland\",\n        countryCode: \"IS\",\n        currencyCode: \"ISK\",\n        currencyNameEn: \"Icelandic króna\",\n        tinType: \"VSK / VASK\",\n        tinName: \"Virðisaukaskattsnúmer\",\n        officialLanguageCode: \"is\",\n        officialLanguageNameEn: \"Icelandic\",\n        officialLanguageNameLocal: \"Íslenska\",\n        countryCallingCode: \"354\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇮🇸\",\n    },\n    {\n        countryNameEn: \"Italy\",\n        countryNameLocal: \"Italia\",\n        countryCode: \"IT\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"P.IVA\",\n        tinName: \"Partita IVA(IVA = Imposta sul Valore Aggiunto)\",\n        officialLanguageCode: \"it\",\n        officialLanguageNameEn: \"Italian\",\n        officialLanguageNameLocal: \"Italiano\",\n        countryCallingCode: \"39\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇮🇹\",\n    },\n    {\n        countryNameEn: \"Jersey\",\n        countryNameLocal: \"Jersey\",\n        countryCode: \"JE\",\n        currencyCode: \"GBP\",\n        currencyNameEn: \"Pound sterling\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"44\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇯🇪\",\n    },\n    {\n        countryNameEn: \"Jamaica\",\n        countryNameLocal: \"Jamaica\",\n        countryCode: \"JM\",\n        currencyCode: \"JMD\",\n        currencyNameEn: \"Jamaican dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"876\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇯🇲\",\n    },\n    {\n        countryNameEn: \"Jordan\",\n        countryNameLocal: \"الأُرْدُن\",\n        countryCode: \"JO\",\n        currencyCode: \"JOD\",\n        currencyNameEn: \"Jordanian dinar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"962\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇯🇴\",\n    },\n    {\n        countryNameEn: \"Japan\",\n        countryNameLocal: \"日本\",\n        countryCode: \"JP\",\n        currencyCode: \"JPY\",\n        currencyNameEn: \"Japanese yen\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ja\",\n        officialLanguageNameEn: \"Japanese\",\n        officialLanguageNameLocal: \"日本語 (にほんご)\",\n        countryCallingCode: \"81\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇯🇵\",\n    },\n    {\n        countryNameEn: \"Kenya\",\n        countryNameLocal: \"Kenya\",\n        countryCode: \"KE\",\n        currencyCode: \"KES\",\n        currencyNameEn: \"Kenyan shilling\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"sw\",\n        officialLanguageNameEn: \"Swahili\",\n        officialLanguageNameLocal: \"Kiswahili\",\n        countryCallingCode: \"254\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇰🇪\",\n    },\n    {\n        countryNameEn: \"Kyrgyzstan\",\n        countryNameLocal: \"Кыргызстан, Киргизия\",\n        countryCode: \"KG\",\n        currencyCode: \"KGS\",\n        currencyNameEn: \"Kyrgyzstani som\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ky\",\n        officialLanguageNameEn: \"Kirghiz, Kyrgyz\",\n        officialLanguageNameLocal: \"Кыргызча, Кыргыз тили\",\n        countryCallingCode: \"996\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇰🇬\",\n    },\n    {\n        countryNameEn: \"Cambodia\",\n        countryNameLocal: \"កម្ពុជា\",\n        countryCode: \"KH\",\n        currencyCode: \"KHR\",\n        currencyNameEn: \"Cambodian riel\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"km\",\n        officialLanguageNameEn: \"Central Khmer\",\n        officialLanguageNameLocal: \"ខ្មែរ, ខេមរភាសា, ភាសាខ្មែរ\",\n        countryCallingCode: \"855\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇰🇭\",\n    },\n    {\n        countryNameEn: \"North Korea\",\n        countryNameLocal: \"조선민주주의인민공화국\",\n        countryCode: \"KP\",\n        currencyCode: \"KPW\",\n        currencyNameEn: \"North Korean won\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ko\",\n        officialLanguageNameEn: \"Korean\",\n        officialLanguageNameLocal: \"조선어\",\n        countryCallingCode: \"850\",\n        areaCodes: [],\n        region: \"Asia\",\n        flag: \"🇰🇵\",\n    },\n    {\n        countryNameEn: \"South Korea\",\n        countryNameLocal: \"대한민국\",\n        countryCode: \"KR\",\n        currencyCode: \"KRW\",\n        currencyNameEn: \"South Korean won\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ko\",\n        officialLanguageNameEn: \"Korean\",\n        officialLanguageNameLocal: \"한국어\",\n        countryCallingCode: \"82\",\n        areaCodes: [],\n        region: \"Asia\",\n        flag: \"🇰🇷\",\n    },\n    {\n        countryNameEn: \"Kiribati\",\n        countryNameLocal: \"Kiribati\",\n        countryCode: \"KI\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"686\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇰🇮\",\n    },\n    {\n        countryNameEn: \"Saint Kitts and Nevis\",\n        countryNameLocal: \"Saint Kitts and Nevis\",\n        countryCode: \"KN\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1869\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇰🇳\",\n    },\n    {\n        countryNameEn: \"Kuwait\",\n        countryNameLocal: \"الكويت\",\n        countryCode: \"KW\",\n        currencyCode: \"KWD\",\n        currencyNameEn: \"Kuwaiti dinar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"965\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇰🇼\",\n    },\n    {\n        countryNameEn: \"Kazakhstan\",\n        countryNameLocal: \"Қазақстан, Казахстан\",\n        countryCode: \"KZ\",\n        currencyCode: \"KZT\",\n        currencyNameEn: \"Kazakhstani tenge\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"kk\",\n        officialLanguageNameEn: \"Kazakh\",\n        officialLanguageNameLocal: \"қазақ тілі\",\n        countryCallingCode: \"7\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇰🇿\",\n    },\n    {\n        countryNameEn: \"Lebanon\",\n        countryNameLocal: \"لبنان, Liban\",\n        countryCode: \"LB\",\n        currencyCode: \"LBP\",\n        currencyNameEn: \"Lebanese pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"961\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇱🇧\",\n    },\n    {\n        countryNameEn: \"Saint Lucia\",\n        countryNameLocal: \"Saint Lucia\",\n        countryCode: \"LC\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1758\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇱🇨\",\n    },\n    {\n        countryNameEn: \"Liechtenstein\",\n        countryNameLocal: \"Liechtenstein\",\n        countryCode: \"LI\",\n        currencyCode: \"CHF\",\n        currencyNameEn: \"Swiss franc\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"de\",\n        officialLanguageNameEn: \"German\",\n        officialLanguageNameLocal: \"Deutsch\",\n        countryCallingCode: \"423\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇱🇮\",\n    },\n    {\n        countryNameEn: \"Sri Lanka\",\n        countryNameLocal: \"ශ්‍රී ලංකා, இலங்கை\",\n        countryCode: \"LK\",\n        currencyCode: \"LKR\",\n        currencyNameEn: \"Sri Lankan rupee\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"si\",\n        officialLanguageNameEn: \"Sinhala, Sinhalese\",\n        officialLanguageNameLocal: \"සිංහල\",\n        countryCallingCode: \"94\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇱🇰\",\n    },\n    {\n        countryNameEn: \"Liberia\",\n        countryNameLocal: \"Liberia\",\n        countryCode: \"LR\",\n        currencyCode: \"LRD\",\n        currencyNameEn: \"Liberian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"231\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇱🇷\",\n    },\n    {\n        countryNameEn: \"Lesotho\",\n        countryNameLocal: \"Lesotho\",\n        countryCode: \"LS\",\n        currencyCode: \"LSL\",\n        currencyNameEn: \"Lesotho loti\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"266\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇱🇸\",\n    },\n    {\n        countryNameEn: \"Lithuania\",\n        countryNameLocal: \"Lietuva\",\n        countryCode: \"LT\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"PVM kodas\",\n        tinName: \"PVM (abbrev. Pridėtinės vertės mokestis) mokėtojo kodas\",\n        officialLanguageCode: \"lt\",\n        officialLanguageNameEn: \"Lithuanian\",\n        officialLanguageNameLocal: \"lietuvių kalba\",\n        countryCallingCode: \"370\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇱🇹\",\n    },\n    {\n        countryNameEn: \"Luxembourg\",\n        countryNameLocal: \"Lëtzebuerg, Luxembourg, Luxemburg\",\n        countryCode: \"LU\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"No. TVA\",\n        tinName: \"Numéro d'identification à la taxe sur la valeur ajoutée\",\n        officialLanguageCode: \"lb\",\n        officialLanguageNameEn: \"Luxembourgish, Letzeburgesch\",\n        officialLanguageNameLocal: \"Lëtzebuergesch\",\n        countryCallingCode: \"352\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇱🇺\",\n    },\n    {\n        countryNameEn: \"Latvia\",\n        countryNameLocal: \"Latvija\",\n        countryCode: \"LV\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"PVN\",\n        tinName: \"Pievienotās vērtības nodokļa (PVN) reģistrācijas numurs\",\n        officialLanguageCode: \"lv\",\n        officialLanguageNameEn: \"Latvian\",\n        officialLanguageNameLocal: \"latviešu valoda\",\n        countryCallingCode: \"371\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇱🇻\",\n    },\n    {\n        countryNameEn: \"Libya\",\n        countryNameLocal: \"ليبيا\",\n        countryCode: \"LY\",\n        currencyCode: \"LYD\",\n        currencyNameEn: \"Libyan dinar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"218\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇱🇾\",\n    },\n    {\n        countryNameEn: \"Morocco\",\n        countryNameLocal: \"Maroc, ⵍⵎⵖⵔⵉⴱ, المغرب\",\n        countryCode: \"MA\",\n        currencyCode: \"MAD\",\n        currencyNameEn: \"Moroccan dirham\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"212\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇲🇦\",\n    },\n    {\n        countryNameEn: \"Monaco\",\n        countryNameLocal: \"Monaco\",\n        countryCode: \"MC\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"377\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇲🇨\",\n    },\n    {\n        countryNameEn: \"Montenegro\",\n        countryNameLocal: \"Crna Gora, Црна Гора\",\n        countryCode: \"ME\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"srp\",\n        officialLanguageNameEn: \"српски језик\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"382\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇲🇪\",\n    },\n    {\n        countryNameEn: \"Saint Martin (French part)\",\n        countryNameLocal: \"Saint-Martin\",\n        countryCode: \"MF\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"590\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇲🇫\",\n    },\n    {\n        countryNameEn: \"Madagascar\",\n        countryNameLocal: \"Madagasikara, Madagascar\",\n        countryCode: \"MG\",\n        currencyCode: \"MGA\",\n        currencyNameEn: \"Malagasy ariary\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"mg\",\n        officialLanguageNameEn: \"Malagasy\",\n        officialLanguageNameLocal: \"fiteny malagasy\",\n        countryCallingCode: \"261\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇲🇬\",\n    },\n    {\n        countryNameEn: \"Mali\",\n        countryNameLocal: \"Mali\",\n        countryCode: \"ML\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"223\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇲🇱\",\n    },\n    {\n        countryNameEn: \"Myanmar\",\n        countryNameLocal: \"မြန်မာ\",\n        countryCode: \"MM\",\n        currencyCode: \"MMK\",\n        currencyNameEn: \"Myanmar kyat\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"my\",\n        officialLanguageNameEn: \"Burmese\",\n        officialLanguageNameLocal: \"ဗမာစာ\",\n        countryCallingCode: \"95\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇲🇲\",\n    },\n    {\n        countryNameEn: \"Mongolia\",\n        countryNameLocal: \"Монгол Улс\",\n        countryCode: \"MN\",\n        currencyCode: \"MNT\",\n        currencyNameEn: \"Mongolian tögrög\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"mn\",\n        officialLanguageNameEn: \"Mongolian\",\n        officialLanguageNameLocal: \"Монгол хэл\",\n        countryCallingCode: \"976\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇲🇳\",\n    },\n    {\n        countryNameEn: \"Macao\",\n        countryNameLocal: \"澳門, Macau\",\n        countryCode: \"MO\",\n        currencyCode: \"MOP\",\n        currencyNameEn: \"Macanese pataca\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"zh-hant\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"853\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇲🇴\",\n    },\n    {\n        countryNameEn: \"Martinique\",\n        countryNameLocal: \"Martinique\",\n        countryCode: \"MQ\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"596\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇲🇶\",\n    },\n    {\n        countryNameEn: \"Mauritania\",\n        countryNameLocal: \"موريتانيا, Mauritanie\",\n        countryCode: \"MR\",\n        currencyCode: \"MRU\",\n        currencyNameEn: \"\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"222\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇲🇷\",\n    },\n    {\n        countryNameEn: \"Montserrat\",\n        countryNameLocal: \"Montserrat\",\n        countryCode: \"MS\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1664\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇲🇸\",\n    },\n    {\n        countryNameEn: \"Malta\",\n        countryNameLocal: \"Malta\",\n        countryCode: \"MT\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"Vat No.\",\n        tinName: \"Vat reg. no.\",\n        officialLanguageCode: \"mt\",\n        officialLanguageNameEn: \"Maltese\",\n        officialLanguageNameLocal: \"Malti\",\n        countryCallingCode: \"356\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇲🇹\",\n    },\n    {\n        countryNameEn: \"Mauritius\",\n        countryNameLocal: \"Maurice, Mauritius\",\n        countryCode: \"MU\",\n        currencyCode: \"MUR\",\n        currencyNameEn: \"Mauritian rupee\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"mfe\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"230\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇲🇺\",\n    },\n    {\n        countryNameEn: \"Maldives\",\n        countryNameLocal: \"\",\n        countryCode: \"MV\",\n        currencyCode: \"MVR\",\n        currencyNameEn: \"Maldivian rufiyaa\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"dv\",\n        officialLanguageNameEn: \"Divehi, Dhivehi, Maldivian\",\n        officialLanguageNameLocal: \"ދިވެހި\",\n        countryCallingCode: \"960\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇲🇻\",\n    },\n    {\n        countryNameEn: \"Malawi\",\n        countryNameLocal: \"Malawi\",\n        countryCode: \"MW\",\n        currencyCode: \"MWK\",\n        currencyNameEn: \"Malawian kwacha\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"265\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇲🇼\",\n    },\n    {\n        countryNameEn: \"Mexico\",\n        countryNameLocal: \"México\",\n        countryCode: \"MX\",\n        currencyCode: \"MXN\",\n        currencyNameEn: \"Mexican peso\",\n        tinType: \"RFC\",\n        tinName: \"Registro Federal de Contribuyentes\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"52\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇲🇽\",\n    },\n    {\n        countryNameEn: \"Malaysia\",\n        countryNameLocal: \"\",\n        countryCode: \"MY\",\n        currencyCode: \"MYR\",\n        currencyNameEn: \"Malaysian ringgit\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ms\",\n        officialLanguageNameEn: \"Malay\",\n        officialLanguageNameLocal: \"Bahasa Melayu, بهاس ملايو‎\",\n        countryCallingCode: \"60\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇲🇾\",\n    },\n    {\n        countryNameEn: \"Mozambique\",\n        countryNameLocal: \"Mozambique\",\n        countryCode: \"MZ\",\n        currencyCode: \"MZN\",\n        currencyNameEn: \"Mozambican metical\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"258\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇲🇿\",\n    },\n    {\n        countryNameEn: \"Namibia\",\n        countryNameLocal: \"Namibia\",\n        countryCode: \"NA\",\n        currencyCode: \"NAD\",\n        currencyNameEn: \"Namibian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"264\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇳🇦\",\n    },\n    {\n        countryNameEn: \"New Caledonia\",\n        countryNameLocal: \"Nouvelle-Calédonie\",\n        countryCode: \"NC\",\n        currencyCode: \"XPF\",\n        currencyNameEn: \"CFP franc (franc Pacifique)\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"687\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇳🇨\",\n    },\n    {\n        countryNameEn: \"Norfolk Island\",\n        countryNameLocal: \"Norfolk Island\",\n        countryCode: \"NF\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"672\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇳🇫\",\n    },\n    {\n        countryNameEn: \"Nigeria\",\n        countryNameLocal: \"Nigeria\",\n        countryCode: \"NG\",\n        currencyCode: \"NGN\",\n        currencyNameEn: \"Nigerian naira\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"234\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇳🇬\",\n    },\n    {\n        countryNameEn: \"Nicaragua\",\n        countryNameLocal: \"Nicaragua\",\n        countryCode: \"NI\",\n        currencyCode: \"NIO\",\n        currencyNameEn: \"Nicaraguan córdoba\",\n        tinType: \"RUC\",\n        tinName: \"Registro Unico de Contribuyentes\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"505\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇳🇮\",\n    },\n    {\n        countryNameEn: \"Norway\",\n        countryNameLocal: \"Norge, Noreg\",\n        countryCode: \"NO\",\n        currencyCode: \"NOK\",\n        currencyNameEn: \"Norwegian krone\",\n        tinType: \"Orgnr\",\n        tinName: \"Organisasjonsnummer\",\n        officialLanguageCode: \"nb\",\n        officialLanguageNameEn: \"Norwegian Bokmål\",\n        officialLanguageNameLocal: \"Norsk Bokmål\",\n        countryCallingCode: \"47\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇳🇴\",\n    },\n    {\n        countryNameEn: \"Nepal\",\n        countryNameLocal: \"\",\n        countryCode: \"NP\",\n        currencyCode: \"NPR\",\n        currencyNameEn: \"Nepalese rupee\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ne\",\n        officialLanguageNameEn: \"Nepali\",\n        officialLanguageNameLocal: \"नेपाली\",\n        countryCallingCode: \"977\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇳🇵\",\n    },\n    {\n        countryNameEn: \"Nauru\",\n        countryNameLocal: \"Nauru\",\n        countryCode: \"NR\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"na\",\n        officialLanguageNameEn: \"Nauru\",\n        officialLanguageNameLocal: \"Dorerin Naoero\",\n        countryCallingCode: \"674\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇳🇷\",\n    },\n    {\n        countryNameEn: \"Niue\",\n        countryNameLocal: \"Niue\",\n        countryCode: \"NU\",\n        currencyCode: \"NZD\",\n        currencyNameEn: \"New Zealand dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"niu\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"683\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇳🇺\",\n    },\n    {\n        countryNameEn: \"New Zealand\",\n        countryNameLocal: \"New Zealand\",\n        countryCode: \"NZ\",\n        currencyCode: \"NZD\",\n        currencyNameEn: \"New Zealand dollar\",\n        tinType: \"NZBN\",\n        tinName: \"NZ Business Number\",\n        officialLanguageCode: \"mi\",\n        officialLanguageNameEn: \"Maori\",\n        officialLanguageNameLocal: \"te reo Māori\",\n        countryCallingCode: \"64\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇳🇿\",\n    },\n    {\n        countryNameEn: \"Oman\",\n        countryNameLocal: \"سلطنة عُمان\",\n        countryCode: \"OM\",\n        currencyCode: \"OMR\",\n        currencyNameEn: \"Omani rial\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"968\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇴🇲\",\n    },\n    {\n        countryNameEn: \"Panama\",\n        countryNameLocal: \"Panama\",\n        countryCode: \"PA\",\n        currencyCode: \"PAB\",\n        currencyNameEn: \"Panamanian balboa\",\n        tinType: \"RUC\",\n        tinName: \"Registro Unico de Contribuyentes\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"507\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇵🇦\",\n    },\n    {\n        countryNameEn: \"Peru\",\n        countryNameLocal: \"Perú\",\n        countryCode: \"PE\",\n        currencyCode: \"PEN\",\n        currencyNameEn: \"Peruvian sol\",\n        tinType: \"RUC\",\n        tinName: \"Registro Unico de Contribuyentes\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"51\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇵🇪\",\n    },\n    {\n        countryNameEn: \"French Polynesia\",\n        countryNameLocal: \"Polynésie française\",\n        countryCode: \"PF\",\n        currencyCode: \"XPF\",\n        currencyNameEn: \"CFP franc (franc Pacifique)\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"689\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇵🇫\",\n    },\n    {\n        countryNameEn: \"Papua New Guinea\",\n        countryNameLocal: \"Papua New Guinea\",\n        countryCode: \"PG\",\n        currencyCode: \"PGK\",\n        currencyNameEn: \"Papua New Guinean kina\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"675\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇵🇬\",\n    },\n    {\n        countryNameEn: \"Pakistan\",\n        countryNameLocal: \"پاکستان\",\n        countryCode: \"PK\",\n        currencyCode: \"PKR\",\n        currencyNameEn: \"Pakistani rupee\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"92\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇵🇰\",\n    },\n    {\n        countryNameEn: \"Poland\",\n        countryNameLocal: \"Polska\",\n        countryCode: \"PL\",\n        currencyCode: \"PLN\",\n        currencyNameEn: \"Polish złoty\",\n        tinType: \"NIP\",\n        tinName: \"numer identyfikacji podatkowej\",\n        officialLanguageCode: \"pl\",\n        officialLanguageNameEn: \"Polish\",\n        officialLanguageNameLocal: \"język polski, polszczyzna\",\n        countryCallingCode: \"48\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇵🇱\",\n    },\n    {\n        countryNameEn: \"Saint Pierre and Miquelon\",\n        countryNameLocal: \"Saint-Pierre-et-Miquelon\",\n        countryCode: \"PM\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"508\",\n        areaCodes: [],\n        region: \"North America\",\n        flag: \"🇵🇲\",\n    },\n    {\n        countryNameEn: \"Pitcairn\",\n        countryNameLocal: \"Pitcairn\",\n        countryCode: \"PN\",\n        currencyCode: \"NZD\",\n        currencyNameEn: \"New Zealand dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"64\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇵🇳\",\n    },\n    {\n        countryNameEn: \"Puerto Rico\",\n        countryNameLocal: \"Puerto Rico\",\n        countryCode: \"PR\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"1\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇵🇷\",\n    },\n    {\n        countryNameEn: \"Palestine, State of\",\n        countryNameLocal: \"Palestinian Territory\",\n        countryCode: \"PS\",\n        currencyCode: \"\",\n        currencyNameEn: \"\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"970\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇵🇸\",\n    },\n    {\n        countryNameEn: \"Portugal\",\n        countryNameLocal: \"Portugal\",\n        countryCode: \"PT\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"NIPC\",\n        tinName: \"Número de Identificação de Pessoa Coletiva (NIPC)\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"351\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇵🇹\",\n    },\n    {\n        countryNameEn: \"Palau\",\n        countryNameLocal: \"Palau\",\n        countryCode: \"PW\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"680\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇵🇼\",\n    },\n    {\n        countryNameEn: \"Paraguay\",\n        countryNameLocal: \"Paraguay\",\n        countryCode: \"PY\",\n        currencyCode: \"PYG\",\n        currencyNameEn: \"Paraguayan guaraní\",\n        tinType: \"RUC\",\n        tinName: \"Registro Unico de Contribuyentes\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"595\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇵🇾\",\n    },\n    {\n        countryNameEn: \"Qatar\",\n        countryNameLocal: \"قطر\",\n        countryCode: \"QA\",\n        currencyCode: \"QAR\",\n        currencyNameEn: \"Qatari riyal\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"974\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇶🇦\",\n    },\n    {\n        countryNameEn: \"Réunion\",\n        countryNameLocal: \"La Réunion\",\n        countryCode: \"RE\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"262\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇷🇪\",\n    },\n    {\n        countryNameEn: \"Romania\",\n        countryNameLocal: \"România\",\n        countryCode: \"RO\",\n        currencyCode: \"RON\",\n        currencyNameEn: \"Romanian leu\",\n        tinType: \"CIF\",\n        tinName: \"Codul de identificare fiscală\",\n        officialLanguageCode: \"ro\",\n        officialLanguageNameEn: \"Romanian, Moldavian, Moldovan\",\n        officialLanguageNameLocal: \"Română\",\n        countryCallingCode: \"40\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇷🇴\",\n    },\n    {\n        countryNameEn: \"Serbia\",\n        countryNameLocal: \"Србија\",\n        countryCode: \"RS\",\n        currencyCode: \"RSD\",\n        currencyNameEn: \"Serbian dinar\",\n        tinType: \"PIB\",\n        tinName: \"Poreski identifikacioni broj\",\n        officialLanguageCode: \"sr\",\n        officialLanguageNameEn: \"Serbian\",\n        officialLanguageNameLocal: \"српски језик\",\n        countryCallingCode: \"381\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇷🇸\",\n    },\n    {\n        countryNameEn: \"Russia\",\n        countryNameLocal: \"Россия\",\n        countryCode: \"RU\",\n        currencyCode: \"RUB\",\n        currencyNameEn: \"Russian ruble\",\n        tinType: \"ИНН\",\n        tinName: \"Идентификационный номер налогоплательщика\",\n        officialLanguageCode: \"ru\",\n        officialLanguageNameEn: \"Russian\",\n        officialLanguageNameLocal: \"русский\",\n        countryCallingCode: \"7\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇷🇺\",\n    },\n    {\n        countryNameEn: \"Rwanda\",\n        countryNameLocal: \"Rwanda\",\n        countryCode: \"RW\",\n        currencyCode: \"RWF\",\n        currencyNameEn: \"Rwandan franc\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"rw\",\n        officialLanguageNameEn: \"Kinyarwanda\",\n        officialLanguageNameLocal: \"Ikinyarwanda\",\n        countryCallingCode: \"250\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇷🇼\",\n    },\n    {\n        countryNameEn: \"Saudi Arabia\",\n        countryNameLocal: \"السعودية\",\n        countryCode: \"SA\",\n        currencyCode: \"SAR\",\n        currencyNameEn: \"Saudi riyal\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"966\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇸🇦\",\n    },\n    {\n        countryNameEn: \"Solomon Islands\",\n        countryNameLocal: \"Solomon Islands\",\n        countryCode: \"SB\",\n        currencyCode: \"SBD\",\n        currencyNameEn: \"Solomon Islands dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"677\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇸🇧\",\n    },\n    {\n        countryNameEn: \"Seychelles\",\n        countryNameLocal: \"Seychelles\",\n        countryCode: \"SC\",\n        currencyCode: \"SCR\",\n        currencyNameEn: \"Seychelles rupee\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"248\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇸🇨\",\n    },\n    {\n        countryNameEn: \"Sweden\",\n        countryNameLocal: \"Sverige\",\n        countryCode: \"SE\",\n        currencyCode: \"SEK\",\n        currencyNameEn: \"Swedish krona/kronor\",\n        tinType: \"Momsnr.\",\n        tinName: \"VAT-nummer\",\n        officialLanguageCode: \"sv\",\n        officialLanguageNameEn: \"Swedish\",\n        officialLanguageNameLocal: \"Svenska\",\n        countryCallingCode: \"46\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇸🇪\",\n    },\n    {\n        countryNameEn: \"Singapore\",\n        countryNameLocal: \"Singapore\",\n        countryCode: \"SG\",\n        currencyCode: \"SGD\",\n        currencyNameEn: \"Singapore dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"zh-hans\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"65\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇸🇬\",\n    },\n    {\n        countryNameEn: \"Saint Helena, Ascension and Tristan da Cunha\",\n        countryNameLocal: \"Saint Helena\",\n        countryCode: \"SH\",\n        currencyCode: \"SHP\",\n        currencyNameEn: \"Saint Helena pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"290\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇸🇭\",\n    },\n    {\n        countryNameEn: \"Slovenia\",\n        countryNameLocal: \"Slovenija\",\n        countryCode: \"SI\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"ID za DDV\",\n        tinName: \"Davčna številka\",\n        officialLanguageCode: \"sl\",\n        officialLanguageNameEn: \"Slovenian\",\n        officialLanguageNameLocal: \"Slovenski Jezik, Slovenščina\",\n        countryCallingCode: \"386\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇸🇮\",\n    },\n    {\n        countryNameEn: \"Svalbard and Jan Mayen\",\n        countryNameLocal: \"Svalbard and Jan Mayen\",\n        countryCode: \"SJ\",\n        currencyCode: \"NOK\",\n        currencyNameEn: \"Norwegian krone\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"no\",\n        officialLanguageNameEn: \"Norwegian\",\n        officialLanguageNameLocal: \"Norsk\",\n        countryCallingCode: \"4779\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇸🇯\",\n    },\n    {\n        countryNameEn: \"Slovakia\",\n        countryNameLocal: \"Slovensko\",\n        countryCode: \"SK\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"IČ DPH\",\n        tinName: \"Identifikačné číslo pre daň z pridanej hodnoty\",\n        officialLanguageCode: \"sk\",\n        officialLanguageNameEn: \"Slovak\",\n        officialLanguageNameLocal: \"Slovenčina, Slovenský Jazyk\",\n        countryCallingCode: \"421\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇸🇰\",\n    },\n    {\n        countryNameEn: \"Sierra Leone\",\n        countryNameLocal: \"Sierra Leone\",\n        countryCode: \"SL\",\n        currencyCode: \"SLL\",\n        currencyNameEn: \"Sierra Leonean leone\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"232\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇸🇱\",\n    },\n    {\n        countryNameEn: \"Republic of San Marino\",\n        countryNameLocal: \"Repubblica di San Marino\",\n        countryCode: \"SM\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"C.O.E.\",\n        tinName: \"Codice operatore economico\",\n        officialLanguageCode: \"it\",\n        officialLanguageNameEn: \"Italian\",\n        officialLanguageNameLocal: \"Italiano\",\n        countryCallingCode: \"378\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇸🇲\",\n    },\n    {\n        countryNameEn: \"Senegal\",\n        countryNameLocal: \"Sénégal\",\n        countryCode: \"SN\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"221\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇸🇳\",\n    },\n    {\n        countryNameEn: \"Somalia\",\n        countryNameLocal: \"Somalia, الصومال\",\n        countryCode: \"SO\",\n        currencyCode: \"SOS\",\n        currencyNameEn: \"Somali shilling\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"so\",\n        officialLanguageNameEn: \"Somali\",\n        officialLanguageNameLocal: \"Soomaaliga, af Soomaali\",\n        countryCallingCode: \"252\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇸🇴\",\n    },\n    {\n        countryNameEn: \"Suriname\",\n        countryNameLocal: \"Suriname\",\n        countryCode: \"SR\",\n        currencyCode: \"SRD\",\n        currencyNameEn: \"Surinamese dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"597\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇸🇷\",\n    },\n    {\n        countryNameEn: \"South Sudan\",\n        countryNameLocal: \"South Sudan\",\n        countryCode: \"SS\",\n        currencyCode: \"SSP\",\n        currencyNameEn: \"South Sudanese pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"211\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇸🇸\",\n    },\n    {\n        countryNameEn: \"Sao Tome and Principe\",\n        countryNameLocal: \"São Tomé e Príncipe\",\n        countryCode: \"ST\",\n        currencyCode: \"STN\",\n        currencyNameEn: \"\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"239\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇸🇹\",\n    },\n    {\n        countryNameEn: \"El Salvador\",\n        countryNameLocal: \"El Salvador\",\n        countryCode: \"SV\",\n        currencyCode: \"SVC\",\n        currencyNameEn: \"Salvadoran colón\",\n        tinType: \"NIT\",\n        tinName: \"Número de Identificación Tributaria\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"503\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇸🇻\",\n    },\n    {\n        countryNameEn: \"Sint Maarten (Dutch part)\",\n        countryNameLocal: \"Sint Maarten\",\n        countryCode: \"SX\",\n        currencyCode: \"ANG\",\n        currencyNameEn: \"Netherlands Antillean guilder\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"1721\",\n        areaCodes: [],\n        region: \"Unknown\",\n        flag: \"🇸🇽\",\n    },\n    {\n        countryNameEn: \"Syrian Arab Republic\",\n        countryNameLocal: \"سوريا, Sūriyya\",\n        countryCode: \"SY\",\n        currencyCode: \"SYP\",\n        currencyNameEn: \"Syrian pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"963\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇸🇾\",\n    },\n    {\n        countryNameEn: \"Chad\",\n        countryNameLocal: \"Tchad, تشاد\",\n        countryCode: \"TD\",\n        currencyCode: \"XAF\",\n        currencyNameEn: \"CFA franc BEAC\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"235\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇹🇩\",\n    },\n    {\n        countryNameEn: \"Togo\",\n        countryNameLocal: \"Togo\",\n        countryCode: \"TG\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"228\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇹🇬\",\n    },\n    {\n        countryNameEn: \"Thailand\",\n        countryNameLocal: \"ประเทศไทย\",\n        countryCode: \"TH\",\n        currencyCode: \"THB\",\n        currencyNameEn: \"Thai baht\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"th\",\n        officialLanguageNameEn: \"Thai\",\n        officialLanguageNameLocal: \"ไทย\",\n        countryCallingCode: \"66\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇭\",\n    },\n    {\n        countryNameEn: \"Tajikistan\",\n        countryNameLocal: \",\",\n        countryCode: \"TJ\",\n        currencyCode: \"TJS\",\n        currencyNameEn: \"Tajikistani somoni\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"tg\",\n        officialLanguageNameEn: \"Tajik\",\n        officialLanguageNameLocal: \"тоҷикӣ, toçikī, تاجیکی‎\",\n        countryCallingCode: \"992\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇯\",\n    },\n    {\n        countryNameEn: \"Tokelau\",\n        countryNameLocal: \"Tokelau\",\n        countryCode: \"TK\",\n        currencyCode: \"NZD\",\n        currencyNameEn: \"New Zealand dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"tkl\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"690\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇰\",\n    },\n    {\n        countryNameEn: \"Timor-Leste\",\n        countryNameLocal: \"Timor-Leste, Timor Lorosa'e\",\n        countryCode: \"TL\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"pt\",\n        officialLanguageNameEn: \"Portuguese\",\n        officialLanguageNameLocal: \"Português\",\n        countryCallingCode: \"670\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇱\",\n    },\n    {\n        countryNameEn: \"Turkmenistan\",\n        countryNameLocal: \"Türkmenistan\",\n        countryCode: \"TM\",\n        currencyCode: \"TMT\",\n        currencyNameEn: \"Turkmenistan manat\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"tk\",\n        officialLanguageNameEn: \"Turkmen\",\n        officialLanguageNameLocal: \"Türkmen, Түркмен\",\n        countryCallingCode: \"993\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇲\",\n    },\n    {\n        countryNameEn: \"Tunisia\",\n        countryNameLocal: \"تونس, Tunisie\",\n        countryCode: \"TN\",\n        currencyCode: \"TND\",\n        currencyNameEn: \"Tunisian dinar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"216\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇹🇳\",\n    },\n    {\n        countryNameEn: \"Tonga\",\n        countryNameLocal: \"Tonga\",\n        countryCode: \"TO\",\n        currencyCode: \"TOP\",\n        currencyNameEn: \"Tongan paʻanga\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"676\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇴\",\n    },\n    {\n        countryNameEn: \"Turkey\",\n        countryNameLocal: \"Türkiye\",\n        countryCode: \"TR\",\n        currencyCode: \"TRY\",\n        currencyNameEn: \"Turkish lira\",\n        tinType: \"KDV\",\n        tinName: \"Vergi Kimlik Numarası\",\n        officialLanguageCode: \"tr\",\n        officialLanguageNameEn: \"Turkish\",\n        officialLanguageNameLocal: \"Türkçe\",\n        countryCallingCode: \"90\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇹🇷\",\n    },\n    {\n        countryNameEn: \"Trinidad and Tobago\",\n        countryNameLocal: \"Trinidad and Tobago\",\n        countryCode: \"TT\",\n        currencyCode: \"TTD\",\n        currencyNameEn: \"Trinidad and Tobago dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"868\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇹🇹\",\n    },\n    {\n        countryNameEn: \"Tuvalu\",\n        countryNameLocal: \"Tuvalu\",\n        countryCode: \"TV\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"688\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇻\",\n    },\n    {\n        countryNameEn: \"United Republic of Tanzania\",\n        countryNameLocal: \"Tanzania\",\n        countryCode: \"TZ\",\n        currencyCode: \"TZS\",\n        currencyNameEn: \"Tanzanian shilling\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"sw\",\n        officialLanguageNameEn: \"Swahili\",\n        officialLanguageNameLocal: \"Kiswahili\",\n        countryCallingCode: \"255\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇹🇿\",\n    },\n    {\n        countryNameEn: \"Ukraine\",\n        countryNameLocal: \"Україна\",\n        countryCode: \"UA\",\n        currencyCode: \"UAH\",\n        currencyNameEn: \"Ukrainian hryvnia\",\n        tinType: \"ІНПП\",\n        tinName: \"Ідентифікаційний номер платника податків\",\n        officialLanguageCode: \"uk\",\n        officialLanguageNameEn: \"Ukrainian\",\n        officialLanguageNameLocal: \"Українська\",\n        countryCallingCode: \"380\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇺🇦\",\n    },\n    {\n        countryNameEn: \"Uganda\",\n        countryNameLocal: \"Uganda\",\n        countryCode: \"UG\",\n        currencyCode: \"UGX\",\n        currencyNameEn: \"Ugandan shilling\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"256\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇺🇬\",\n    },\n    {\n        countryNameEn: \"United States of America\",\n        countryNameLocal: \"United States of America\",\n        countryCode: \"US\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"EIN\",\n        tinName: \"Tax Identification Number\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1\",\n        areaCodes: [],\n        region: \"North America\",\n        flag: \"🇺🇸\",\n    },\n    {\n        countryNameEn: \"Uruguay\",\n        countryNameLocal: \"Uruguay\",\n        countryCode: \"UY\",\n        currencyCode: \"UYU\",\n        currencyNameEn: \"Uruguayan peso\",\n        tinType: \"RUT\",\n        tinName: \"Registro Único Tributario\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"598\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇺🇾\",\n    },\n    {\n        countryNameEn: \"Uzbekistan\",\n        countryNameLocal: \"\",\n        countryCode: \"UZ\",\n        currencyCode: \"UZS\",\n        currencyNameEn: \"Uzbekistan som\",\n        tinType: \"СТИР\",\n        tinName: \"Солиқ тўловчиларнинг идентификация рақами\",\n        officialLanguageCode: \"uz\",\n        officialLanguageNameEn: \"Uzbek\",\n        officialLanguageNameLocal: \"Oʻzbek, Ўзбек, أۇزبېك‎\",\n        countryCallingCode: \"998\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇺🇿\",\n    },\n    {\n        countryNameEn: \"Saint Vincent and the Grenadines\",\n        countryNameLocal: \"Saint Vincent and the Grenadines\",\n        countryCode: \"VC\",\n        currencyCode: \"XCD\",\n        currencyNameEn: \"East Caribbean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1784\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇻🇨\",\n    },\n    {\n        countryNameEn: \"Venezuela (Bolivarian Republic of)\",\n        countryNameLocal: \"Venezuela\",\n        countryCode: \"VE\",\n        currencyCode: \"VES\",\n        currencyNameEn: \"\",\n        tinType: \"RIF\",\n        tinName: \"Registro de Informacion Fiscal\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"58\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇻🇪\",\n    },\n    {\n        countryNameEn: \"Virgin Islands (British)\",\n        countryNameLocal: \"British Virgin Islands\",\n        countryCode: \"VG\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1284\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇻🇬\",\n    },\n    {\n        countryNameEn: \"Virgin Islands (U.S.)\",\n        countryNameLocal: \"United States Virgin Islands\",\n        countryCode: \"VI\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1340\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇻🇮\",\n    },\n    {\n        countryNameEn: \"Vietnam\",\n        countryNameLocal: \"Việt Nam\",\n        countryCode: \"VN\",\n        currencyCode: \"VND\",\n        currencyNameEn: \"Vietnamese đồng\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"vi\",\n        officialLanguageNameEn: \"Vietnamese\",\n        officialLanguageNameLocal: \"Tiếng Việt\",\n        countryCallingCode: \"84\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇻🇳\",\n    },\n    {\n        countryNameEn: \"Vanuatu\",\n        countryNameLocal: \"Vanuatu\",\n        countryCode: \"VU\",\n        currencyCode: \"VUV\",\n        currencyNameEn: \"Vanuatu vatu\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"bi\",\n        officialLanguageNameEn: \"Bislama\",\n        officialLanguageNameLocal: \"Bislama\",\n        countryCallingCode: \"678\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇻🇺\",\n    },\n    {\n        countryNameEn: \"Wallis and Futuna\",\n        countryNameLocal: \"Wallis-et-Futuna\",\n        countryCode: \"WF\",\n        currencyCode: \"XPF\",\n        currencyNameEn: \"CFP franc (franc Pacifique)\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"681\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇼🇫\",\n    },\n    {\n        countryNameEn: \"Samoa\",\n        countryNameLocal: \"Samoa\",\n        countryCode: \"WS\",\n        currencyCode: \"WST\",\n        currencyNameEn: \"Samoan tala\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"sm\",\n        officialLanguageNameEn: \"Samoan\",\n        officialLanguageNameLocal: \"gagana fa'a Samoa\",\n        countryCallingCode: \"685\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇼🇸\",\n    },\n    {\n        countryNameEn: \"Yemen\",\n        countryNameLocal: \"اليَمَن\",\n        countryCode: \"YE\",\n        currencyCode: \"YER\",\n        currencyNameEn: \"Yemeni rial\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"967\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇾🇪\",\n    },\n    {\n        countryNameEn: \"Mayotte\",\n        countryNameLocal: \"Mayotte\",\n        countryCode: \"YT\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"262\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇾🇹\",\n    },\n    {\n        countryNameEn: \"South Africa\",\n        countryNameLocal: \"South Africa\",\n        countryCode: \"ZA\",\n        currencyCode: \"ZAR\",\n        currencyNameEn: \"South African rand\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"27\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇿🇦\",\n    },\n    {\n        countryNameEn: \"Zambia\",\n        countryNameLocal: \"Zambia\",\n        countryCode: \"ZM\",\n        currencyCode: \"ZMW\",\n        currencyNameEn: \"Zambian kwacha\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"260\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇿🇲\",\n    },\n    {\n        countryNameEn: \"Zimbabwe\",\n        countryNameLocal: \"Zimbabwe\",\n        countryCode: \"ZW\",\n        currencyCode: \"ZWL\",\n        currencyNameEn: \"Zimbabwean dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"263\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇿🇼\",\n    },\n    {\n        countryNameEn: \"Eswatini\",\n        countryNameLocal: \"Swaziland\",\n        countryCode: \"SZ\",\n        currencyCode: \"SZL\",\n        currencyNameEn: \"Swazi lilangeni\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"268\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇸🇿\",\n    },\n    {\n        countryNameEn: \"North Macedonia\",\n        countryNameLocal: \"Македонија\",\n        countryCode: \"MK\",\n        currencyCode: \"MKD\",\n        currencyNameEn: \"Macedonian denar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"mk\",\n        officialLanguageNameEn: \"Macedonian\",\n        officialLanguageNameLocal: \"македонски јазик\",\n        countryCallingCode: \"389\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇲🇰\",\n    },\n    {\n        countryNameEn: \"Philippines\",\n        countryNameLocal: \"Philippines\",\n        countryCode: \"PH\",\n        currencyCode: \"PHP\",\n        currencyNameEn: \"Philippine peso\",\n        tinType: \"TIN\",\n        tinName: \"Tax Identification Number\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"63\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇵🇭\",\n    },\n    {\n        countryNameEn: \"Netherlands\",\n        countryNameLocal: \"Nederland\",\n        countryCode: \"NL\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"Btw-nr.\",\n        tinName: \"Btw-nummer\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"31\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇳🇱\",\n    },\n    {\n        countryNameEn: \"United Arab Emirates\",\n        countryNameLocal: \"دولة الإمارات العربيّة المتّحدة\",\n        countryCode: \"AE\",\n        currencyCode: \"AED\",\n        currencyNameEn: \"United Arab Emirates dirham\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"971\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇦🇪\",\n    },\n    {\n        countryNameEn: \"Republic of Moldova\",\n        countryNameLocal: \"Moldova, Молдавия\",\n        countryCode: \"MD\",\n        currencyCode: \"MDL\",\n        currencyNameEn: \"Moldovan leu\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ro\",\n        officialLanguageNameEn: \"Romanian, Moldavian, Moldovan\",\n        officialLanguageNameLocal: \"Română\",\n        countryCallingCode: \"373\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇲🇩\",\n    },\n    {\n        countryNameEn: \"Gambia\",\n        countryNameLocal: \"The Gambia\",\n        countryCode: \"GM\",\n        currencyCode: \"GMD\",\n        currencyNameEn: \"Gambian dalasi\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"220\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇬🇲\",\n    },\n    {\n        countryNameEn: \"Dominican Republic\",\n        countryNameLocal: \"República Dominicana\",\n        countryCode: \"DO\",\n        currencyCode: \"DOP\",\n        currencyNameEn: \"Dominican peso\",\n        tinType: \"RNC\",\n        tinName: \"Registro Nacional del Contribuyente\",\n        officialLanguageCode: \"es\",\n        officialLanguageNameEn: \"Spanish, Castilian\",\n        officialLanguageNameLocal: \"Español\",\n        countryCallingCode: \"1\",\n        areaCodes: [],\n        region: \"South/Latin America\",\n        flag: \"🇩🇴\",\n    },\n    {\n        countryNameEn: \"Sudan\",\n        countryNameLocal: \"السودان\",\n        countryCode: \"SD\",\n        currencyCode: \"SDG\",\n        currencyNameEn: \"Sudanese pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"ar\",\n        officialLanguageNameEn: \"Arabic\",\n        officialLanguageNameLocal: \"العربية\",\n        countryCallingCode: \"249\",\n        areaCodes: [],\n        region: \"Arab States\",\n        flag: \"🇸🇩\",\n    },\n    {\n        countryNameEn: \"Lao People's Democratic Republic\",\n        countryNameLocal: \"ປະຊາຊົນລາວ\",\n        countryCode: \"LA\",\n        currencyCode: \"LAK\",\n        currencyNameEn: \"Lao kip\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"lo\",\n        officialLanguageNameEn: \"Lao\",\n        officialLanguageNameLocal: \"ພາສາລາວ\",\n        countryCallingCode: \"856\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇱🇦\",\n    },\n    {\n        countryNameEn: \"Taiwan, Province of China\",\n        countryNameLocal: \"Taiwan\",\n        countryCode: \"TW\",\n        currencyCode: \"TWD\",\n        currencyNameEn: \"New Taiwan dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"zh-hant\",\n        officialLanguageNameEn: \"\",\n        officialLanguageNameLocal: \"\",\n        countryCallingCode: \"886\",\n        areaCodes: [],\n        region: \"Asia & Pacific\",\n        flag: \"🇹🇼\",\n    },\n    {\n        countryNameEn: \"Republic of the Congo\",\n        countryNameLocal: \"République du Congo\",\n        countryCode: \"CG\",\n        currencyCode: \"XAF\",\n        currencyNameEn: \"CFA franc BEAC\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"242\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇨🇬\",\n    },\n    {\n        countryNameEn: \"Czechia\",\n        countryNameLocal: \"Česká republika\",\n        countryCode: \"CZ\",\n        currencyCode: \"CZK\",\n        currencyNameEn: \"Czech koruna\",\n        tinType: \"DIČ\",\n        tinName: \"Daňové identifikační číslo\",\n        officialLanguageCode: \"cs\",\n        officialLanguageNameEn: \"Czech\",\n        officialLanguageNameLocal: \"Čeština\",\n        countryCallingCode: \"420\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇨🇿\",\n    },\n    {\n        countryNameEn: \"United Kingdom\",\n        countryNameLocal: \"Great Britain\",\n        countryCode: \"GB\",\n        currencyCode: \"GBP\",\n        currencyNameEn: \"Pound sterling\",\n        tinType: \"VAT Reg No\",\n        tinName: \"Value added tax registration number\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"44\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇬🇧\",\n    },\n    {\n        countryNameEn: \"Niger\",\n        countryNameLocal: \"Niger\",\n        countryCode: \"NE\",\n        currencyCode: \"XOF\",\n        currencyNameEn: \"CFA franc BCEAO\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"227\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇳🇪\",\n    },\n    {\n        countryNameEn: \"Democratic Republic of the Congo\",\n        countryNameLocal: \"Democratic Republic of the Congo\",\n        countryCode: \"CD\",\n        currencyCode: \"CDF\",\n        currencyNameEn: \"Congolese franc\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"243\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇨🇩\",\n    },\n    {\n        countryNameEn: \"Commonwealth of The Bahamas\",\n        countryNameLocal: \"Commonwealth of The Bahamas\",\n        countryCode: \"BS\",\n        currencyCode: \"BSD\",\n        currencyNameEn: \"Bahamian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1 242\",\n        areaCodes: [],\n        region: \"Caribbean\",\n        flag: \"🇧🇸\",\n    },\n    {\n        countryNameEn: \"Cocos (Keeling) Islands\",\n        countryNameLocal: \"Pulu Kokos (Keeling)\",\n        countryCode: \"CC\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"61 891\",\n        areaCodes: [],\n        region: \"Australia\",\n        flag: \"🇨🇨\",\n    },\n    {\n        countryNameEn: \"Central African Republic\",\n        countryNameLocal: \"République centrafricaine\",\n        countryCode: \"CF\",\n        currencyCode: \"XAF\",\n        currencyNameEn: \"Central African CFA\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"236\",\n        areaCodes: [],\n        region: \"Africa\",\n        flag: \"🇨🇫\",\n    },\n    {\n        countryNameEn: \"Cook Islands\",\n        countryNameLocal: \"Kūki 'Āirani\",\n        countryCode: \"CK\",\n        currencyCode: \"NZD\",\n        currencyNameEn: \"New Zealand dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"682\",\n        areaCodes: [],\n        region: \"South Pacific Ocean\",\n        flag: \"🇨🇰\",\n    },\n    {\n        countryNameEn: \"Falkland Islands\",\n        countryNameLocal: \"Falkland Islands\",\n        countryCode: \"FK\",\n        currencyCode: \"FKP\",\n        currencyNameEn: \"Falklands pound\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"500\",\n        areaCodes: [],\n        region: \"South Atlantic Ocean\",\n        flag: \"🇫🇰\",\n    },\n    {\n        countryNameEn: \"Faroe Islands\",\n        countryNameLocal: \"Færøerne\",\n        countryCode: \"FO\",\n        currencyCode: \"DKK\",\n        currencyNameEn: \"Faroese króna\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"da\",\n        officialLanguageNameEn: \"Danish\",\n        officialLanguageNameLocal: \"dansk\",\n        countryCallingCode: \"298\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇫🇴\",\n    },\n    {\n        countryNameEn: \"Territory of Heard Island and McDonald Islands\",\n        countryNameLocal: \"Territory of Heard Island and McDonald Islands\",\n        countryCode: \"HM\",\n        currencyCode: \"AUD\",\n        currencyNameEn: \"Australian dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"672\",\n        areaCodes: [],\n        region: \"Indian Ocean\",\n        flag: \"🇭🇲\",\n    },\n    {\n        countryNameEn: \"British Indian Ocean Territory\",\n        countryNameLocal: \"British Indian Ocean Territory\",\n        countryCode: \"IO\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States Dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"246\",\n        areaCodes: [],\n        region: \"Indian Ocean\",\n        flag: \"🇮🇴\",\n    },\n    {\n        countryNameEn: \"Comoros\",\n        countryNameLocal: \"Umoja wa Komori\",\n        countryCode: \"KM\",\n        currencyCode: \"KMF\",\n        currencyNameEn: \"Comorian franc\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"269\",\n        areaCodes: [],\n        region: \"Indian Ocean\",\n        flag: \"🇰🇲\",\n    },\n    {\n        countryNameEn: \"Cayman Islands\",\n        countryNameLocal: \"Cayman Islands\",\n        countryCode: \"KY\",\n        currencyCode: \"KYD\",\n        currencyNameEn: \"Cayman Islands dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1 345\",\n        areaCodes: [],\n        region: \"Caribbean Sea\",\n        flag: \"🇰🇾\",\n    },\n    {\n        countryNameEn: \"Republic of the Marshall Islands\",\n        countryNameLocal: \"Aolepān Aorōkin Ṃajeḷ\",\n        countryCode: \"MH\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"692\",\n        areaCodes: [],\n        region: \"Pacific Ocean\",\n        flag: \"🇲🇭\",\n    },\n    {\n        countryNameEn: \"Commonwealth of the Northern Mariana Islands\",\n        countryNameLocal: \"Sankattan Siha Na Islas Mariånas\",\n        countryCode: \"MP\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1 670\",\n        areaCodes: [],\n        region: \"Pacific Ocean\",\n        flag: \"🇲🇵\",\n    },\n    {\n        countryNameEn: \"Turks and Caicos Islands\",\n        countryNameLocal: \"Turks and Caicos Islands\",\n        countryCode: \"TC\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1 649\",\n        areaCodes: [],\n        region: \"Atlantic Ocean\",\n        flag: \"🇹🇨\",\n    },\n    {\n        countryNameEn: \"French Southern and Antarctic Lands\",\n        countryNameLocal: \"Terres australes et antarctiques françaises\",\n        countryCode: \"TF\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"fr\",\n        officialLanguageNameEn: \"French\",\n        officialLanguageNameLocal: \"Français\",\n        countryCallingCode: \"672\",\n        areaCodes: [],\n        region: \"Indian Ocean\",\n        flag: \"🇹🇫\",\n    },\n    {\n        countryNameEn: \"United States Minor Outlying Islands\",\n        countryNameLocal: \"United States Minor Outlying Islands\",\n        countryCode: \"UM\",\n        currencyCode: \"USD\",\n        currencyNameEn: \"United States dollar\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"en\",\n        officialLanguageNameEn: \"English\",\n        officialLanguageNameLocal: \"English\",\n        countryCallingCode: \"1\",\n        areaCodes: [],\n        region: \"Pacific Ocean\",\n        flag: \"🇺🇲\",\n    },\n    {\n        countryNameEn: \"Holy See\",\n        countryNameLocal: \"Sancta Sedes\",\n        countryCode: \"VA\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"la\",\n        officialLanguageNameEn: \"Latin\",\n        officialLanguageNameLocal: \"lingua latīna\",\n        countryCallingCode: \"39\",\n        areaCodes: [],\n        region: \"Europe\",\n        flag: \"🇻🇦\",\n    },\n    {\n        countryNameEn: \"Republic of Kosovo\",\n        countryNameLocal: \"Republika e Kosovës\",\n        countryCode: \"XK\",\n        currencyCode: \"EUR\",\n        currencyNameEn: \"Euro\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"sq\",\n        officialLanguageNameEn: \"Albanian\",\n        officialLanguageNameLocal: \"Shqip\",\n        countryCallingCode: \"383\",\n        region: \"Europe\",\n        flag: \"🇽🇰\",\n    },\n    {\n        countryNameEn: \"Netherlands Antilles\",\n        countryNameLocal: \"Nederlandse Antillen\",\n        countryCode: \"AN\",\n        currencyCode: \"ANG\",\n        currencyNameEn: \"Netherlands Antillean guilder\",\n        tinType: \"\",\n        tinName: \"\",\n        officialLanguageCode: \"nl\",\n        officialLanguageNameEn: \"Dutch, Flemish\",\n        officialLanguageNameLocal: \"Nederlands, Vlaams\",\n        countryCallingCode: \"599\",\n        region: \"Europe\",\n        flag: \"🇧🇶\",\n    },\n];\nexports.default = countriesData;\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.customList = exports.customArray = exports.findOne = exports.filter = exports.all = exports.utils = void 0;\nconst groupBy_1 = __importDefault(require(\"./utils/groupBy\"));\nconst supplant_1 = __importDefault(require(\"./utils/supplant\"));\nconst countriesData_1 = __importDefault(require(\"./countriesData\"));\nexports.utils = {\n    groupBy: groupBy_1.default,\n};\nfunction all() {\n    return countriesData_1.default;\n}\nexports.all = all;\nfunction filter(countryProperty, value) {\n    return countriesData_1.default.filter((countryData) => countryData[countryProperty] === value);\n}\nexports.filter = filter;\nfunction findOne(countryProperty, value) {\n    return countriesData_1.default.find((countryData) => countryData[countryProperty] === value);\n}\nexports.findOne = findOne;\nfunction customArray(fields = {\n    name: \"{countryNameEn} ({countryCode})\",\n    value: \"{countryCode}\",\n}, { sortBy, sortDataBy, filter: filterFunc, } = {}) {\n    const finalCollection = [];\n    let data = countriesData_1.default;\n    if (typeof filterFunc === \"function\") {\n        data = data.filter(filterFunc);\n    }\n    if (sortDataBy) {\n        const collator = new Intl.Collator([], { sensitivity: \"accent\" });\n        data.sort((a, b) => collator.compare(a[sortDataBy], b[sortDataBy]));\n    }\n    data.forEach((countryData) => {\n        const collectionObject = {};\n        for (const field in fields) {\n            collectionObject[field] = (0, supplant_1.default)(fields[field], countryData);\n        }\n        finalCollection.push(collectionObject);\n    });\n    if (sortBy && fields[sortBy]) {\n        const collator = new Intl.Collator([], { sensitivity: \"accent\" });\n        finalCollection.sort((a, b) => collator.compare(a[sortBy], b[sortBy]));\n    }\n    return finalCollection;\n}\nexports.customArray = customArray;\nfunction customList(key = \"countryCode\", label = \"{countryNameEn} ({countryCode})\", { filter: filterFunc } = {}) {\n    const finalObject = {};\n    let data = countriesData_1.default;\n    if (typeof filterFunc === \"function\") {\n        data = data.filter(filterFunc);\n    }\n    data.forEach((countryData) => {\n        const value = (0, supplant_1.default)(label, countryData);\n        finalObject[String(countryData[key])] = value;\n    });\n    return finalObject;\n}\nexports.customList = customList;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAO5D,aAAS,QAAQ,OAAO,KAAK;AACzB,aAAO,MAAM,OAAO,CAAC,QAAQ,SAAS;AAClC,cAAM,WAAW,OAAO,KAAK,GAAG,CAAC;AACjC,YAAI,CAAC,OAAO,QAAQ,GAAG;AACnB,iBAAO,QAAQ,IAAI,CAAC;AAAA,QACxB;AACA,eAAO,QAAQ,EAAE,KAAK,IAAI;AAC1B,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AAAA,IACT;AACA,YAAQ,UAAU;AAAA;AAAA;;;AClBlB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAO5D,aAAS,SAAS,UAAU,MAAM;AAC9B,aAAO,SAAS,QAAQ,eAAe,CAAC,OAAO,QAAQ;AACnD,cAAM,QAAQ,KAAK,GAAG;AACtB,eAAO,OAAO,UAAU,YAAY,OAAO,UAAU,WAC/C,MAAM,SAAS,IACf;AAAA,MACV,CAAC;AAAA,IACL;AACA,YAAQ,UAAU;AAAA;AAAA;;;AChBlB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAM,gBAAgB;AAAA,MAClB;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,QACA,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,MACA;AAAA,QACI,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AAAA,IACJ;AACA,YAAQ,UAAU;AAAA;AAAA;;;AC5+HlB;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,mBAAoB,SAAU,KAAK;AACnE,aAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,WAAW,IAAI;AAAA,IAC5D;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa,QAAQ,cAAc,QAAQ,UAAU,QAAQ,SAAS,QAAQ,MAAM,QAAQ,QAAQ;AAC5G,QAAM,YAAY,gBAAgB,iBAA0B;AAC5D,QAAM,aAAa,gBAAgB,kBAA2B;AAC9D,QAAM,kBAAkB,gBAAgB,uBAA0B;AAClE,YAAQ,QAAQ;AAAA,MACZ,SAAS,UAAU;AAAA,IACvB;AACA,aAAS,MAAM;AACX,aAAO,gBAAgB;AAAA,IAC3B;AACA,YAAQ,MAAM;AACd,aAAS,OAAO,iBAAiB,OAAO;AACpC,aAAO,gBAAgB,QAAQ,OAAO,CAAC,gBAAgB,YAAY,eAAe,MAAM,KAAK;AAAA,IACjG;AACA,YAAQ,SAAS;AACjB,aAAS,QAAQ,iBAAiB,OAAO;AACrC,aAAO,gBAAgB,QAAQ,KAAK,CAAC,gBAAgB,YAAY,eAAe,MAAM,KAAK;AAAA,IAC/F;AACA,YAAQ,UAAU;AAClB,aAAS,YAAY,SAAS;AAAA,MAC1B,MAAM;AAAA,MACN,OAAO;AAAA,IACX,GAAG,EAAE,QAAQ,YAAY,QAAQ,WAAY,IAAI,CAAC,GAAG;AACjD,YAAM,kBAAkB,CAAC;AACzB,UAAI,OAAO,gBAAgB;AAC3B,UAAI,OAAO,eAAe,YAAY;AAClC,eAAO,KAAK,OAAO,UAAU;AAAA,MACjC;AACA,UAAI,YAAY;AACZ,cAAM,WAAW,IAAI,KAAK,SAAS,CAAC,GAAG,EAAE,aAAa,SAAS,CAAC;AAChE,aAAK,KAAK,CAAC,GAAG,MAAM,SAAS,QAAQ,EAAE,UAAU,GAAG,EAAE,UAAU,CAAC,CAAC;AAAA,MACtE;AACA,WAAK,QAAQ,CAAC,gBAAgB;AAC1B,cAAM,mBAAmB,CAAC;AAC1B,mBAAW,SAAS,QAAQ;AACxB,2BAAiB,KAAK,KAAK,GAAG,WAAW,SAAS,OAAO,KAAK,GAAG,WAAW;AAAA,QAChF;AACA,wBAAgB,KAAK,gBAAgB;AAAA,MACzC,CAAC;AACD,UAAI,UAAU,OAAO,MAAM,GAAG;AAC1B,cAAM,WAAW,IAAI,KAAK,SAAS,CAAC,GAAG,EAAE,aAAa,SAAS,CAAC;AAChE,wBAAgB,KAAK,CAAC,GAAG,MAAM,SAAS,QAAQ,EAAE,MAAM,GAAG,EAAE,MAAM,CAAC,CAAC;AAAA,MACzE;AACA,aAAO;AAAA,IACX;AACA,YAAQ,cAAc;AACtB,aAAS,WAAW,MAAM,eAAe,QAAQ,mCAAmC,EAAE,QAAQ,WAAW,IAAI,CAAC,GAAG;AAC7G,YAAM,cAAc,CAAC;AACrB,UAAI,OAAO,gBAAgB;AAC3B,UAAI,OAAO,eAAe,YAAY;AAClC,eAAO,KAAK,OAAO,UAAU;AAAA,MACjC;AACA,WAAK,QAAQ,CAAC,gBAAgB;AAC1B,cAAM,SAAS,GAAG,WAAW,SAAS,OAAO,WAAW;AACxD,oBAAY,OAAO,YAAY,GAAG,CAAC,CAAC,IAAI;AAAA,MAC5C,CAAC;AACD,aAAO;AAAA,IACX;AACA,YAAQ,aAAa;AAAA;AAAA;", "names": []}
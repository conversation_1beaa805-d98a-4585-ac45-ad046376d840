<!--  client/layers/production/pages/production/resources/equipment.vue -->
<template>
  <div class="dark:bg-black bg-muted-10 min-h-screen">
    <!-- Page Header -->
    <div
      class="border-b border-muted-200 dark:border-muted-800 bg-white dark:bg-black"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div>
            <BaseHeading
              size="lg"
              weight="semibold"
              class="text-muted-800 dark:text-white"
            >
              Equipment Management
            </BaseHeading>
            <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
              Manage and track production equipment with GPS tracking
            </BaseText>
          </div>
          <div class="flex items-center gap-3">
            <BaseButton variant="primary" @click="showNewEquipmentModal = true">
              <Icon name="lucide:plus" class="size-4 mr-2" />
              Add Equipment
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Filters Card -->
      <BaseCard class="mb-6">
        <div class="p-6">
          <BaseHeading
            size="sm"
            weight="medium"
            class="mb-4 text-muted-800 dark:text-white"
          >
            Filters
          </BaseHeading>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search -->
            <BaseInput
              v-model="filters.search"
              placeholder="Search equipment..."
              icon="lucide:search"
              rounded="md"
            />

            <!-- Type Filter -->
            <BaseListbox
              v-model="filters.type"
              placeholder="Filter by Type"
              rounded="md"
            >
              <BaseListboxItem value="">All Types</BaseListboxItem>
              <BaseListboxItem value="VEHICLE">Vehicles</BaseListboxItem>
              <BaseListboxItem value="EQUIPMENT">Equipment</BaseListboxItem>
              <BaseListboxItem value="TOOL">Tools</BaseListboxItem>
              <BaseListboxItem value="MACHINERY">Machinery</BaseListboxItem>
              <BaseListboxItem value="DEVICE">Devices</BaseListboxItem>
              <BaseListboxItem value="OTHER">Other</BaseListboxItem>
            </BaseListbox>

            <!-- Status Filter -->
            <BaseListbox
              v-model="filters.status"
              placeholder="Filter by Status"
              rounded="md"
            >
              <BaseListboxItem value="">All Statuses</BaseListboxItem>
              <BaseListboxItem value="ACTIVE">Active</BaseListboxItem>
              <BaseListboxItem value="INACTIVE">Inactive</BaseListboxItem>
              <BaseListboxItem value="MAINTENANCE">Maintenance</BaseListboxItem>
              <BaseListboxItem value="REPAIR">Repair</BaseListboxItem>
              <BaseListboxItem value="LOST">Lost</BaseListboxItem>
              <BaseListboxItem value="STOLEN">Stolen</BaseListboxItem>
              <BaseListboxItem value="DISPOSED">Disposed</BaseListboxItem>
            </BaseListbox>

            <!-- GPS Tracking Filter -->
            <BaseListbox
              v-model="filters.tracking"
              placeholder="GPS Tracking"
              rounded="md"
            >
              <BaseListboxItem value="">All Equipment</BaseListboxItem>
              <BaseListboxItem value="tracked">GPS Tracked</BaseListboxItem>
              <BaseListboxItem value="untracked">Not Tracked</BaseListboxItem>
            </BaseListbox>
          </div>

          <!-- Applied Filters -->
          <div v-if="hasActiveFilters" class="flex flex-wrap gap-2 mt-4">
            <BaseTag
              v-if="filters.search"
              variant="primary"
              size="sm"
              @click="filters.search = ''"
            >
              Search: {{ filters.search }}
              <Icon name="lucide:x" class="size-3 ml-1" />
            </BaseTag>
            <BaseTag
              v-if="filters.type"
              variant="primary"
              size="sm"
              @click="filters.type = ''"
            >
              Type: {{ filters.type }}
              <Icon name="lucide:x" class="size-3 ml-1" />
            </BaseTag>
            <BaseTag
              v-if="filters.status"
              variant="primary"
              size="sm"
              @click="filters.status = ''"
            >
              Status: {{ formatStatus(filters.status) }}
              <Icon name="lucide:x" class="size-3 ml-1" />
            </BaseTag>
            <BaseTag
              v-if="filters.tracking"
              variant="primary"
              size="sm"
              @click="filters.tracking = ''"
            >
              Tracking: {{ filters.tracking }}
              <Icon name="lucide:x" class="size-3 ml-1" />
            </BaseTag>
            <BaseButton
              v-if="hasActiveFilters"
              variant="muted"
              size="sm"
              @click="clearFilters"
            >
              Clear All
            </BaseButton>
          </div>
        </div>
      </BaseCard>

      <!-- View Toggle and Stats -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-4">
          <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
            {{ totalItems }} equipment items
          </BaseText>
          <div v-if="loading" class="flex items-center gap-2">
            <Icon
              name="lucide:loader-2"
              class="size-4 animate-spin text-primary-500"
            />
            <BaseText size="sm" class="text-muted-500">Loading...</BaseText>
          </div>
        </div>

        <BaseButtonGroup>
          <BaseButton
            :variant="viewMode === 'grid' ? 'primary' : 'muted'"
            size="sm"
            @click="viewMode = 'grid'"
          >
            <Icon name="lucide:grid-3x3" class="size-4" />
          </BaseButton>
          <BaseButton
            :variant="viewMode === 'list' ? 'primary' : 'muted'"
            size="sm"
            @click="viewMode = 'list'"
          >
            <Icon name="lucide:list" class="size-4" />
          </BaseButton>
        </BaseButtonGroup>
      </div>

      <!-- Error State -->
      <BaseCard v-if="error" class="mb-6">
        <div class="p-6">
          <div class="flex items-center gap-3 text-red-600 dark:text-red-400">
            <Icon name="lucide:alert-circle" class="size-5" />
            <BaseText weight="medium">{{ error }}</BaseText>
          </div>
          <BaseButton
            variant="muted"
            size="sm"
            class="mt-3"
            @click="fetchEquipment"
          >
            <Icon name="lucide:refresh-cw" class="size-4 mr-2" />
            Try Again
          </BaseButton>
        </div>
      </BaseCard>

      <!-- Empty State -->
      <BaseCard v-else-if="!loading && equipment.length === 0" class="mb-6">
        <div class="p-12 text-center">
          <Icon
            name="lucide:package"
            class="size-16 text-muted-300 dark:text-muted-700 mx-auto mb-4"
          />
          <BaseHeading
            size="lg"
            weight="medium"
            class="text-muted-800 dark:text-white mb-2"
          >
            No Equipment Found
          </BaseHeading>
          <BaseText
            class="text-muted-500 dark:text-muted-400 max-w-md mx-auto mb-6"
          >
            There is no equipment matching your filters. Try adjusting your
            filters or add new equipment to get started.
          </BaseText>
          <BaseButton variant="primary" @click="showNewEquipmentModal = true">
            <Icon name="lucide:plus" class="size-4 mr-2" />
            Add Equipment
          </BaseButton>
        </div>
      </BaseCard>

      <!-- Equipment Content -->
      <template v-if="!loading && !error && equipment.length > 0">
        <!-- Grid View -->
        <div
          v-if="viewMode === 'grid'"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
        >
          <EquipmentCard
            v-for="item in equipment"
            :key="item.id"
            :equipment="item"
            @view="openEquipmentDetails"
            @edit="editEquipment"
            @delete="deleteEquipment"
          />
        </div>

        <!-- List View -->
        <BaseCard v-else class="mb-8">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b border-muted-200 dark:border-muted-700">
                  <th
                    class="text-left p-4 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Equipment
                  </th>
                  <th
                    class="text-left p-4 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Type
                  </th>
                  <th
                    class="text-left p-4 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Status
                  </th>
                  <th
                    class="text-left p-4 font-medium text-muted-700 dark:text-muted-300"
                  >
                    GPS Tracking
                  </th>
                  <th
                    class="text-left p-4 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Location
                  </th>
                  <th
                    class="text-left p-4 font-medium text-muted-700 dark:text-muted-300"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in equipment"
                  :key="item.id"
                  class="border-b border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50"
                >
                  <td class="p-4">
                    <div class="flex items-center gap-3">
                      <div
                        class="flex items-center justify-center w-10 h-10 rounded-lg bg-primary-100 dark:bg-primary-900/20"
                      >
                        <Icon
                          name="lucide:package"
                          class="size-5 text-primary-600 dark:text-primary-400"
                        />
                      </div>
                      <div>
                        <BaseText
                          weight="medium"
                          class="text-muted-900 dark:text-white"
                        >
                          {{ item.name }}
                        </BaseText>
                        <BaseText
                          size="sm"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          {{ item.serialNumber || "No S/N" }}
                        </BaseText>
                      </div>
                    </div>
                  </td>
                  <td class="p-4">
                    <BaseTag variant="muted" size="sm">
                      {{ formatType(item.type) }}
                    </BaseTag>
                  </td>
                  <td class="p-4">
                    <BaseTag variant="primary" size="sm">
                      {{ formatStatus(item.status) }}
                    </BaseTag>
                  </td>
                  <td class="p-4">
                    <div class="flex items-center gap-2">
                      <div
                        class="w-2 h-2 rounded-full"
                        :class="
                          item.gpsDevices?.length > 0
                            ? 'bg-green-500'
                            : 'bg-gray-400'
                        "
                      />
                      <BaseText
                        size="sm"
                        class="text-muted-500 dark:text-muted-400"
                      >
                        {{
                          item.gpsDevices?.length > 0
                            ? "Tracked"
                            : "Not tracked"
                        }}
                      </BaseText>
                    </div>
                  </td>
                  <td class="p-4">
                    <BaseText
                      size="sm"
                      class="text-muted-500 dark:text-muted-400"
                    >
                      {{ item.currentLocation?.address || "Unknown" }}
                    </BaseText>
                  </td>
                  <td class="p-4">
                    <div class="flex items-center gap-2">
                      <BaseButton
                        variant="muted"
                        size="sm"
                        @click="openEquipmentDetails(item)"
                      >
                        <Icon name="lucide:eye" class="size-4" />
                      </BaseButton>
                      <BaseButton
                        variant="muted"
                        size="sm"
                        @click="editEquipment(item)"
                      >
                        <Icon name="lucide:edit" class="size-4" />
                      </BaseButton>
                      <BaseButton
                        variant="muted"
                        size="sm"
                        @click="deleteEquipment(item)"
                      >
                        <Icon name="lucide:trash-2" class="size-4" />
                      </BaseButton>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </BaseCard>
      </template>

      <!-- Pagination -->
      <div v-if="equipment.length > 0" class="flex justify-center">
        <BasePagination
          :current-page="currentPage"
          :total-pages="Math.ceil(totalItems / perPage)"
          @prev="currentPage > 1 && (currentPage -= 1)"
          @next="
            currentPage < Math.ceil(totalItems / perPage) && (currentPage += 1)
          "
        />
      </div>
    </div>

    <!-- Equipment Detail Modal -->
    <EquipmentDetailModal
      :is-open="showEquipmentDetailModal"
      :equipment-id="selectedEquipmentId"
      @close="closeEquipmentDetailModal"
      @refresh="fetchEquipment"
    />

    <!-- New Equipment Modal -->
    <NewEquipmentModal
      :is-open="showNewEquipmentModal"
      @close="showNewEquipmentModal = false"
      @created="handleEquipmentCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { format, parseISO, isAfter, addDays } from "date-fns";
import { useProductionStore } from "../../../stores/useProductionStore";
import EquipmentCard from "../../../components/resources/EquipmentCard.vue";
import EquipmentDetailModal from "../../../components/resources/EquipmentDetailModal.vue";
import NewEquipmentModal from "../../../components/resources/NewEquipmentModal.vue";

const productionStore = useProductionStore();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const equipment = ref<any[]>([]);
const showEquipmentDetailModal = ref(false);
const showNewEquipmentModal = ref(false);
const selectedEquipmentId = ref("");
const currentPage = ref(1);
const perPage = ref(9);
const totalItems = ref(0);
const viewMode = ref<"grid" | "list">("grid");
const filters = ref({
  search: "",
  type: "",
  status: "",
  tracking: "",
});

// Computed
const hasActiveFilters = computed(() => {
  return (
    filters.value.search ||
    filters.value.type ||
    filters.value.status ||
    filters.value.tracking
  );
});

// Methods
const fetchEquipment = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Prepare filters
    const apiFilters: Record<string, any> = {
      page: currentPage.value,
      limit: perPage.value,
    };

    if (filters.value.search) {
      apiFilters.search = filters.value.search;
    }

    if (filters.value.type) {
      apiFilters.type = filters.value.type;
    }

    if (filters.value.status) {
      apiFilters.status = filters.value.status;
    }

    if (filters.value.tracking) {
      apiFilters.tracking = filters.value.tracking;
    }

    // Fetch equipment
    const response = (await productionStore.getEquipment(apiFilters)) as any;
    equipment.value = response?.data || [];
    totalItems.value = response?.total || equipment.value.length;
  } catch (err) {
    console.error("Error fetching equipment:", err);
    error.value = "Failed to load equipment. Please try again.";
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  try {
    return format(parseISO(dateString), "MMM d, yyyy");
  } catch (error) {
    return dateString;
  }
};

const formatStatus = (status: string) => {
  if (!status) return "Unknown";

  const statusMap: Record<string, string> = {
    OPERATIONAL: "Operational",
    MAINTENANCE: "In Maintenance",
    REPAIR: "Under Repair",
    INACTIVE: "Inactive",
    RESERVED: "Reserved",
  };

  return statusMap[status] || status.replace(/_/g, " ");
};

const getStatusColor = (status: string) => {
  if (!status) return "muted";

  const colorMap: Record<string, string> = {
    OPERATIONAL: "success",
    MAINTENANCE: "warning",
    REPAIR: "danger",
    INACTIVE: "muted",
    RESERVED: "info",
  };

  return colorMap[status] || "muted";
};

const formatCondition = (condition: string) => {
  if (!condition) return "Unknown";

  const conditionMap: Record<string, string> = {
    EXCELLENT: "Excellent",
    GOOD: "Good",
    FAIR: "Fair",
    POOR: "Poor",
    CRITICAL: "Critical",
  };

  return conditionMap[condition] || condition.replace(/_/g, " ");
};

const getConditionIcon = (condition: string) => {
  if (!condition) return "ph:question-duotone";

  const iconMap: Record<string, string> = {
    EXCELLENT: "ph:star-duotone",
    GOOD: "ph:thumbs-up-duotone",
    FAIR: "ph:hand-duotone",
    POOR: "ph:warning-duotone",
    CRITICAL: "ph:x-circle-duotone",
  };

  return iconMap[condition] || "ph:question-duotone";
};

const getConditionIconClass = (condition: string) => {
  if (!condition) return "text-muted-400";

  const classMap: Record<string, string> = {
    EXCELLENT: "text-success-500",
    GOOD: "text-success-400",
    FAIR: "text-warning-400",
    POOR: "text-warning-500",
    CRITICAL: "text-danger-500",
  };

  return classMap[condition] || "text-muted-400";
};

const getConditionTextClass = (condition: string) => {
  if (!condition) return "text-muted-500 dark:text-muted-400";

  const classMap: Record<string, string> = {
    EXCELLENT: "text-success-500",
    GOOD: "text-success-400",
    FAIR: "text-warning-400",
    POOR: "text-warning-500",
    CRITICAL: "text-danger-500",
  };

  return classMap[condition] || "text-muted-500 dark:text-muted-400";
};

const getNextMaintenanceClass = (equipment: any) => {
  if (!equipment.nextMaintenanceDate)
    return "text-muted-500 dark:text-muted-400";

  // If next maintenance is within 30 days, show warning
  const thirtyDaysFromNow = addDays(new Date(), 30);
  try {
    const nextMaintenance = parseISO(equipment.nextMaintenanceDate);
    if (isAfter(thirtyDaysFromNow, nextMaintenance)) {
      return "text-warning-500";
    }

    // If next maintenance is overdue, show danger
    if (isAfter(new Date(), nextMaintenance)) {
      return "text-danger-500";
    }
  } catch (error) {
    // If date parsing fails, return default
  }

  return "text-muted-500 dark:text-muted-400";
};

const clearFilters = () => {
  filters.value = {
    search: "",
    type: "",
    status: "",
    tracking: "",
  };
  currentPage.value = 1;
};

// Add missing functions
const formatType = (type: string) => {
  if (!type) return "Unknown";
  return type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
};

const getStatusVariant = (status: string) => {
  const variantMap: Record<string, string> = {
    ACTIVE: "primary",
    INACTIVE: "muted",
    MAINTENANCE: "muted",
    REPAIR: "muted",
    LOST: "muted",
    STOLEN: "muted",
    DISPOSED: "muted",
  };
  return variantMap[status] || "muted";
};

const editEquipment = (item: any) => {
  selectedEquipmentId.value = item.id;
  showNewEquipmentModal.value = true;
};

const deleteEquipment = async (item: any) => {
  if (confirm(`Are you sure you want to delete ${item.name}?`)) {
    try {
      await productionStore.deleteEquipment(item.id);
      await fetchEquipment();
    } catch (error) {
      console.error("Error deleting equipment:", error);
    }
  }
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchEquipment();
};

const openEquipmentDetails = (item: any) => {
  selectedEquipmentId.value = item.id;
  showEquipmentDetailModal.value = true;
};

const closeEquipmentDetailModal = () => {
  showEquipmentDetailModal.value = false;
  selectedEquipmentId.value = "";
};

const handleEquipmentCreated = () => {
  fetchEquipment();
};

// Watch for filter changes
watch(
  filters,
  () => {
    currentPage.value = 1;
    fetchEquipment();
  },
  { deep: true }
);

// Initial fetch
onMounted(() => {
  fetchEquipment();
});
</script>

<!--  client/layers/production/pages/production/resources/equipment.vue -->
<template>
  <div class="dark:bg-black bg-muted-10 min-h-screen">
    <!-- Page Header -->
    <div class="border-b border-muted-200 dark:border-muted-800 bg-white dark:bg-black">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div>
            <BaseHeading size="lg" weight="semibold" class="text-muted-800 dark:text-white">
              Equipment Management
            </BaseHeading>
            <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
              Manage and track production equipment with GPS tracking
            </BaseText>
          </div>
          <div class="flex items-center gap-3">
            <BaseButton variant="primary" @click="showNewEquipmentModal = true">
              <Icon name="lucide:plus" class="size-4 mr-2" />
              Add Equipment
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Filters Card -->
      <BaseCard class="mb-6">
        <div class="p-6">
          <BaseHeading size="sm" weight="medium" class="mb-4 text-muted-800 dark:text-white">
            Filters
          </BaseHeading>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search -->
            <BaseInput
              v-model="filters.search"
              placeholder="Search equipment..."
              icon="lucide:search"
              rounded="md"
            />

            <!-- Type Filter -->
            <BaseListbox
              v-model="filters.type"
              placeholder="Filter by Type"
              rounded="md"
            >
              <BaseListboxItem value="">All Types</BaseListboxItem>
              <BaseListboxItem value="VEHICLE">Vehicles</BaseListboxItem>
              <BaseListboxItem value="EQUIPMENT">Equipment</BaseListboxItem>
              <BaseListboxItem value="TOOL">Tools</BaseListboxItem>
              <BaseListboxItem value="MACHINERY">Machinery</BaseListboxItem>
              <BaseListboxItem value="DEVICE">Devices</BaseListboxItem>
              <BaseListboxItem value="OTHER">Other</BaseListboxItem>
            </BaseListbox>

            <!-- Status Filter -->
            <BaseListbox
              v-model="filters.status"
              placeholder="Filter by Status"
              rounded="md"
            >
              <BaseListboxItem value="">All Statuses</BaseListboxItem>
              <BaseListboxItem value="ACTIVE">Active</BaseListboxItem>
              <BaseListboxItem value="INACTIVE">Inactive</BaseListboxItem>
              <BaseListboxItem value="MAINTENANCE">Maintenance</BaseListboxItem>
              <BaseListboxItem value="REPAIR">Repair</BaseListboxItem>
              <BaseListboxItem value="LOST">Lost</BaseListboxItem>
              <BaseListboxItem value="STOLEN">Stolen</BaseListboxItem>
              <BaseListboxItem value="DISPOSED">Disposed</BaseListboxItem>
            </BaseListbox>

            <!-- GPS Tracking Filter -->
            <BaseListbox
              v-model="filters.tracking"
              placeholder="GPS Tracking"
              rounded="md"
            >
              <BaseListboxItem value="">All Equipment</BaseListboxItem>
              <BaseListboxItem value="tracked">GPS Tracked</BaseListboxItem>
              <BaseListboxItem value="untracked">Not Tracked</BaseListboxItem>
            </BaseListbox>
          </div>

      <!-- Applied Filters -->
      <div v-if="hasActiveFilters" class="flex flex-wrap gap-2 mt-3">
        <BaseTag
          v-if="filters.search"
          color="info"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.search = ''"
        >
          Search: {{ filters.search }}
        </BaseTag>
        <BaseTag
          v-if="filters.type"
          color="primary"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.type = ''"
        >
          Type: {{ filters.type }}
        </BaseTag>
        <BaseTag
          v-if="filters.status"
          color="warning"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.status = ''"
        >
          Status: {{ formatStatus(filters.status) }}
        </BaseTag>
        <BaseTag
          v-if="filters.condition"
          color="success"
          flavor="pastel"
          size="sm"
          removable
          @remove="filters.condition = ''"
        >
          Condition: {{ formatCondition(filters.condition) }}
        </BaseTag>
        <BaseButton
          v-if="hasActiveFilters"
          color="muted"
          flavor="link"
          size="sm"
          @click="clearFilters"
        >
          Clear All
        </BaseButton>
      </div>
    </div>

    <!-- View Toggle -->
    <div class="flex justify-end mb-4">
      <div class="inline-flex rounded-lg overflow-hidden">
        <button
          class="px-4 py-2 text-sm font-medium"
          :class="
            viewMode === 'grid'
              ? 'bg-primary-500 text-white'
              : 'bg-muted-100 dark:bg-muted-800 text-muted-500 dark:text-muted-400 hover:bg-muted-200 dark:hover:bg-muted-700'
          "
          @click="viewMode = 'grid'"
        >
          <Icon name="ph:grid-four-duotone" class="h-4 w-4" />
        </button>
        <button
          class="px-4 py-2 text-sm font-medium"
          :class="
            viewMode === 'list'
              ? 'bg-primary-500 text-white'
              : 'bg-muted-100 dark:bg-muted-800 text-muted-500 dark:text-muted-400 hover:bg-muted-200 dark:hover:bg-muted-700'
          "
          @click="viewMode = 'list'"
        >
          <Icon name="ph:list-bullets-duotone" class="h-4 w-4" />
        </button>
      </div>
    </div>

    <!-- Equipment List -->
    <div v-if="loading" class="flex justify-center py-8">
      <BaseButtonIcon shape="rounded" color="primary" loading />
    </div>

    <div
      v-else-if="error"
      class="bg-danger-100 dark:bg-danger-500/20 text-danger-500 p-4 rounded-lg mb-6"
    >
      <div class="flex items-center">
        <Icon name="ph:warning-circle-duotone" class="h-5 w-5 mr-2" />
        <BaseText>{{ error }}</BaseText>
      </div>
      <BaseButton
        color="danger"
        flavor="link"
        class="mt-2"
        @click="fetchEquipment"
      >
        Try Again
      </BaseButton>
    </div>

    <div v-else-if="equipment.length === 0" class="text-center py-12">
      <Icon
        name="ph:gear-six-duotone"
        class="h-16 w-16 text-muted-300 dark:text-muted-700 mx-auto mb-4"
      />
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        class="text-muted-800 dark:text-white mb-2"
      >
        No Equipment Found
      </BaseHeading>
      <BaseParagraph
        class="text-muted-500 dark:text-muted-400 max-w-md mx-auto"
      >
        There is no equipment matching your filters. Try adjusting your filters
        or add new equipment.
      </BaseParagraph>
      <BaseButton
        color="primary"
        class="mt-4"
        @click="showNewEquipmentModal = true"
      >
        <Icon name="ph:plus-duotone" class="h-4 w-4 mr-1" />
        Add Equipment
      </BaseButton>
    </div>

    <template v-else>
      <!-- Grid View -->
      <div
        v-if="viewMode === 'grid'"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <EquipmentCard
          v-for="item in equipment"
          :key="item.id"
          :equipment="item"
          @view="openEquipmentDetails"
        />
      </div>

      <!-- List View -->
      <BaseCard v-else>
        <div class="overflow-x-auto">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-muted-200 dark:border-muted-700">
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Name
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Type
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Status
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Condition
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Location
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Next Maintenance
                </th>
                <th class="p-3 font-medium text-muted-700 dark:text-muted-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in equipment"
                :key="item.id"
                class="border-b border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50"
              >
                <td class="p-3">
                  <BaseText
                    weight="medium"
                    class="text-muted-900 dark:text-white"
                  >
                    {{ item.name }}
                  </BaseText>
                </td>
                <td class="p-3">
                  <BaseText class="text-muted-500 dark:text-muted-400">
                    {{ item.type }}
                  </BaseText>
                </td>
                <td class="p-3">
                  <BaseTag
                    :color="getStatusColor(item.status)"
                    flavor="pastel"
                    size="sm"
                  >
                    {{ formatStatus(item.status) }}
                  </BaseTag>
                </td>
                <td class="p-3">
                  <div class="flex items-center">
                    <Icon
                      :name="getConditionIcon(item.condition)"
                      :class="getConditionIconClass(item.condition)"
                      class="h-4 w-4 mr-1"
                    />
                    <BaseText :class="getConditionTextClass(item.condition)">
                      {{ formatCondition(item.condition) }}
                    </BaseText>
                  </div>
                </td>
                <td class="p-3">
                  <BaseText class="text-muted-500 dark:text-muted-400">
                    {{ item.location || "N/A" }}
                  </BaseText>
                </td>
                <td class="p-3">
                  <BaseText :class="getNextMaintenanceClass(item)">
                    {{ formatDate(item.nextMaintenanceDate) }}
                  </BaseText>
                </td>
                <td class="p-3">
                  <BaseButton
                    color="primary"
                    flavor="link"
                    @click="openEquipmentDetails(item)"
                  >
                    View
                  </BaseButton>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </BaseCard>
    </template>

    <!-- Pagination -->
    <div v-if="equipment.length > 0" class="mt-6 flex justify-center">
      <BasePagination
        v-model="currentPage"
        :total-items="totalItems"
        :per-page="perPage"
        :max-links="5"
        @update:model-value="handlePageChange"
      />
    </div>

    <!-- Equipment Detail Modal -->
    <EquipmentDetailModal
      :is-open="showEquipmentDetailModal"
      :equipment-id="selectedEquipmentId"
      @close="closeEquipmentDetailModal"
      @refresh="fetchEquipment"
    />

    <!-- New Equipment Modal -->
    <NewEquipmentModal
      :is-open="showNewEquipmentModal"
      @close="showNewEquipmentModal = false"
      @created="handleEquipmentCreated"
    />
  </BaseLayerPage>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { format, parseISO, isAfter, addDays } from "date-fns";
import { useProductionStore } from "../../../stores/useProductionStore";
import EquipmentCard from "../../../components/resources/EquipmentCard.vue";
import EquipmentDetailModal from "../../../components/resources/EquipmentDetailModal.vue";
import NewEquipmentModal from "../../../components/resources/NewEquipmentModal.vue";

const productionStore = useProductionStore();

// State
const loading = ref(false);
const error = ref<string | null>(null);
const equipment = ref<any[]>([]);
const showEquipmentDetailModal = ref(false);
const showNewEquipmentModal = ref(false);
const selectedEquipmentId = ref("");
const currentPage = ref(1);
const perPage = ref(9);
const totalItems = ref(0);
const viewMode = ref<"grid" | "list">("grid");
const filters = ref({
  search: "",
  type: "",
  status: "",
  condition: "",
});

// Computed
const hasActiveFilters = computed(() => {
  return (
    filters.value.search ||
    filters.value.type ||
    filters.value.status ||
    filters.value.condition
  );
});

// Methods
const fetchEquipment = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Prepare filters
    const apiFilters: Record<string, any> = {
      page: currentPage.value,
      limit: perPage.value,
    };

    if (filters.value.search) {
      apiFilters.search = filters.value.search;
    }

    if (filters.value.type) {
      apiFilters.type = filters.value.type;
    }

    if (filters.value.status) {
      apiFilters.status = filters.value.status;
    }

    if (filters.value.condition) {
      apiFilters.condition = filters.value.condition;
    }

    // Fetch equipment
    const response = await productionStore.getEquipment(apiFilters);
    equipment.value = response.data || [];
    totalItems.value = response.total || equipment.value.length;
  } catch (err) {
    console.error("Error fetching equipment:", err);
    error.value = "Failed to load equipment. Please try again.";
  } finally {
    loading.value = false;
  }
};

const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";
  try {
    return format(parseISO(dateString), "MMM d, yyyy");
  } catch (error) {
    return dateString;
  }
};

const formatStatus = (status: string) => {
  if (!status) return "Unknown";

  const statusMap: Record<string, string> = {
    OPERATIONAL: "Operational",
    MAINTENANCE: "In Maintenance",
    REPAIR: "Under Repair",
    INACTIVE: "Inactive",
    RESERVED: "Reserved",
  };

  return statusMap[status] || status.replace(/_/g, " ");
};

const getStatusColor = (status: string) => {
  if (!status) return "muted";

  const colorMap: Record<string, string> = {
    OPERATIONAL: "success",
    MAINTENANCE: "warning",
    REPAIR: "danger",
    INACTIVE: "muted",
    RESERVED: "info",
  };

  return colorMap[status] || "muted";
};

const formatCondition = (condition: string) => {
  if (!condition) return "Unknown";

  const conditionMap: Record<string, string> = {
    EXCELLENT: "Excellent",
    GOOD: "Good",
    FAIR: "Fair",
    POOR: "Poor",
    CRITICAL: "Critical",
  };

  return conditionMap[condition] || condition.replace(/_/g, " ");
};

const getConditionIcon = (condition: string) => {
  if (!condition) return "ph:question-duotone";

  const iconMap: Record<string, string> = {
    EXCELLENT: "ph:star-duotone",
    GOOD: "ph:thumbs-up-duotone",
    FAIR: "ph:hand-duotone",
    POOR: "ph:warning-duotone",
    CRITICAL: "ph:x-circle-duotone",
  };

  return iconMap[condition] || "ph:question-duotone";
};

const getConditionIconClass = (condition: string) => {
  if (!condition) return "text-muted-400";

  const classMap: Record<string, string> = {
    EXCELLENT: "text-success-500",
    GOOD: "text-success-400",
    FAIR: "text-warning-400",
    POOR: "text-warning-500",
    CRITICAL: "text-danger-500",
  };

  return classMap[condition] || "text-muted-400";
};

const getConditionTextClass = (condition: string) => {
  if (!condition) return "text-muted-500 dark:text-muted-400";

  const classMap: Record<string, string> = {
    EXCELLENT: "text-success-500",
    GOOD: "text-success-400",
    FAIR: "text-warning-400",
    POOR: "text-warning-500",
    CRITICAL: "text-danger-500",
  };

  return classMap[condition] || "text-muted-500 dark:text-muted-400";
};

const getNextMaintenanceClass = (equipment: any) => {
  if (!equipment.nextMaintenanceDate)
    return "text-muted-500 dark:text-muted-400";

  // If next maintenance is within 30 days, show warning
  const thirtyDaysFromNow = addDays(new Date(), 30);
  try {
    const nextMaintenance = parseISO(equipment.nextMaintenanceDate);
    if (isAfter(thirtyDaysFromNow, nextMaintenance)) {
      return "text-warning-500";
    }

    // If next maintenance is overdue, show danger
    if (isAfter(new Date(), nextMaintenance)) {
      return "text-danger-500";
    }
  } catch (error) {
    // If date parsing fails, return default
  }

  return "text-muted-500 dark:text-muted-400";
};

const clearFilters = () => {
  filters.value = {
    search: "",
    type: "",
    status: "",
    condition: "",
  };
  currentPage.value = 1;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchEquipment();
};

const openEquipmentDetails = (item: any) => {
  selectedEquipmentId.value = item.id;
  showEquipmentDetailModal.value = true;
};

const closeEquipmentDetailModal = () => {
  showEquipmentDetailModal.value = false;
  selectedEquipmentId.value = "";
};

const handleEquipmentCreated = () => {
  fetchEquipment();
};

// Watch for filter changes
watch(
  filters,
  () => {
    currentPage.value = 1;
    fetchEquipment();
  },
  { deep: true }
);

// Initial fetch
onMounted(() => {
  fetchEquipment();
});
</script>

<!--  client/layers/timemanagement/pages/timemanagement/locations.vue -->

<template>
  <BaseLayerPage
    title="Location Tracking"
    description="Monitor worker locations and compliance"
  >
    <template #title-right>
      <div class="flex items-center gap-2">
        <BaseSelect v-model="selectedView" class="w-40">
          <option value="live">Live View</option>
          <option value="history">Location History</option>
          <option value="alerts">Compliance Alerts</option>
        </BaseSelect>
        <BaseButton color="primary" @click="refreshMap">
          <Icon name="ph:arrows-clockwise-duotone" class="h-4 w-4" />
        </BaseButton>
      </div>
    </template>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Map View -->
      <div class="lg:col-span-2">
        <BaseCard class="p-4">
          <div class="flex items-center justify-between mb-4">
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              {{ getViewTitle() }}
            </BaseHeading>
            <div class="flex items-center gap-2">
              <BaseSelect
                v-if="selectedView === 'history'"
                v-model="selectedDate"
                class="w-40"
              >
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="thisWeek">This Week</option>
                <option value="custom">Custom Date</option>
              </BaseSelect>
              <BaseSelect v-model="selectedLocation" class="w-48">
                <option value="all">All Locations</option>
                <option
                  v-for="location in workLocations"
                  :key="location.id"
                  :value="location.id"
                >
                  {{ location.name }}
                </option>
              </BaseSelect>
            </div>
          </div>

          <div
            class="h-[600px] rounded-lg overflow-hidden border border-muted-200 dark:border-muted-700"
          >
            <MapComponent
              ref="mapRef"
              :mapbox-token="mapboxToken"
              :map-style="mapStyle"
              :locations="mapLocations"
              :center="mapCenter"
              :zoom="12"
              @marker-click="handleMarkerClick"
            />
          </div>
        </BaseCard>
      </div>

      <!-- Worker List / Alerts -->
      <div class="lg:col-span-1">
        <BaseCard class="p-4">
          <BaseHeading
            as="h3"
            size="sm"
            weight="medium"
            class="text-muted-800 dark:text-white mb-4"
          >
            {{
              selectedView === "alerts" ? "Compliance Alerts" : "Active Workers"
            }}
          </BaseHeading>

          <div v-if="isLoading" class="flex justify-center py-6">
            <BaseLoader size="lg" />
          </div>
          <div
            v-else-if="selectedView === 'alerts' && locationAlerts.length === 0"
            class="py-6 text-center"
          >
            <div
              class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted-100 dark:bg-muted-800"
            >
              <Icon
                name="ph:check-circle-duotone"
                class="h-6 w-6 text-success-500"
              />
            </div>
            <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
              No compliance alerts found
            </BaseText>
          </div>
          <div
            v-else-if="selectedView !== 'alerts' && activeWorkers.length === 0"
            class="py-6 text-center"
          >
            <div
              class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted-100 dark:bg-muted-800"
            >
              <Icon
                name="ph:users-duotone"
                class="h-6 w-6 text-muted-400 dark:text-muted-500"
              />
            </div>
            <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
              No active workers found
            </BaseText>
          </div>
          <div v-else-if="selectedView === 'alerts'" class="space-y-3">
            <LocationAlertItem
              v-for="alert in locationAlerts"
              :key="alert.id"
              :alert="alert"
              @view="focusWorkerOnMap"
            />
          </div>
          <div v-else class="space-y-3">
            <WorkerLocationItem
              v-for="worker in activeWorkers"
              :key="worker.id"
              :worker="worker"
              @view="focusWorkerOnMap"
            />
          </div>
        </BaseCard>

        <!-- Work Locations -->
        <BaseCard class="p-4 mt-6">
          <div class="flex items-center justify-between mb-4">
            <BaseHeading
              as="h3"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Work Locations
            </BaseHeading>
            <BaseButton
              color="primary"
              size="sm"
              to="/timemanagement/settings/locations"
            >
              Manage
            </BaseButton>
          </div>

          <div v-if="isLoading" class="flex justify-center py-6">
            <BaseLoader size="lg" />
          </div>
          <div v-else-if="workLocations.length === 0" class="py-6 text-center">
            <div
              class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted-100 dark:bg-muted-800"
            >
              <Icon
                name="ph:map-pin-duotone"
                class="h-6 w-6 text-muted-400 dark:text-muted-500"
              />
            </div>
            <BaseText size="sm" class="text-muted-500 dark:text-muted-400">
              No work locations defined
            </BaseText>
          </div>
          <div v-else class="space-y-3">
            <WorkLocationItem
              v-for="location in workLocations"
              :key="location.id"
              :location="location"
              :is-selected="selectedLocation === location.id"
              @select="selectWorkLocation"
            />
          </div>
        </BaseCard>
      </div>
    </div>
  </BaseLayerPage>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import BaseLayerPage from "../../../../.app/app/components/BaseLayerPage.vue";
import LocationAlertItem from "../components/locations/LocationAlertItem.vue";
import WorkerLocationItem from "../components/locations/WorkerLocationItem.vue";
import WorkLocationItem from "../components/locations/WorkLocationItem.vue";
import MapComponent from "../components/map/MapComponent.vue";

// State
const isLoading = ref(true);
const isMapLoading = ref(true);
const selectedView = ref("live");
const selectedDate = ref("today");
const selectedLocation = ref("all");
const mapRef = ref(null);
const selectedWorkerId = ref(null);

// Mapbox token
const mapboxToken = ref(
  "pk.eyJ1IjoidGltb2xhbWJpbmciLCJhIjoiY205bXZsODltMGhqajJxc2ExanhtaHFraCJ9.VpdmvJs4bUp7p73HOqKo2A"
);
const mapStyle = ref("streets-v12");

// Mock data
const activeWorkers = ref([
  {
    id: "1",
    name: "John Smith",
    avatar: "https://placehold.co/100x100/4267B2/FFFFFF?text=JS",
    project: "Office Building Construction",
    task: "Foundation Work",
    location: {
      latitude: 40.7128,
      longitude: -74.006,
      timestamp: new Date(new Date().getTime() - 5 * 60000).toISOString(), // 5 minutes ago
    },
    isCompliant: true,
  },
  {
    id: "2",
    name: "Sarah Johnson",
    avatar: "https://placehold.co/100x100/E1306C/FFFFFF?text=SJ",
    project: "Residential Complex",
    task: "Electrical Installation",
    location: {
      latitude: 40.7138,
      longitude: -74.007,
      timestamp: new Date(new Date().getTime() - 10 * 60000).toISOString(), // 10 minutes ago
    },
    isCompliant: true,
  },
  {
    id: "3",
    name: "Michael Brown",
    avatar: "https://placehold.co/100x100/34A853/FFFFFF?text=MB",
    project: "Highway Bridge",
    task: "Steel Framework",
    location: {
      latitude: 40.7148,
      longitude: -74.008,
      timestamp: new Date(new Date().getTime() - 15 * 60000).toISOString(), // 15 minutes ago
    },
    isCompliant: false,
  },
  {
    id: "4",
    name: "Emily Davis",
    avatar: "https://placehold.co/100x100/FF0000/FFFFFF?text=ED",
    project: "Shopping Mall",
    task: "Interior Finishing",
    location: {
      latitude: 40.7158,
      longitude: -74.009,
      timestamp: new Date(new Date().getTime() - 20 * 60000).toISOString(), // 20 minutes ago
    },
    isCompliant: true,
  },
  {
    id: "5",
    name: "David Wilson",
    avatar: "https://placehold.co/100x100/0077B5/FFFFFF?text=DW",
    project: "Hospital Renovation",
    task: "Plumbing Work",
    location: {
      latitude: 40.7168,
      longitude: -74.01,
      timestamp: new Date(new Date().getTime() - 25 * 60000).toISOString(), // 25 minutes ago
    },
    isCompliant: false,
  },
]);

const locationAlerts = ref([
  {
    id: "1",
    worker: {
      id: "3",
      name: "Michael Brown",
      avatar: "https://placehold.co/100x100/34A853/FFFFFF?text=MB",
    },
    project: "Highway Bridge",
    task: "Steel Framework",
    location: {
      latitude: 40.7148,
      longitude: -74.008,
      timestamp: new Date(new Date().getTime() - 15 * 60000).toISOString(), // 15 minutes ago
    },
    workLocation: {
      name: "Highway Bridge Site",
    },
    distance: 1.2, // km from work location
    type: "OUTSIDE_AREA",
  },
  {
    id: "2",
    worker: {
      id: "5",
      name: "David Wilson",
      avatar: "https://placehold.co/100x100/0077B5/FFFFFF?text=DW",
    },
    project: "Hospital Renovation",
    task: "Plumbing Work",
    location: {
      latitude: 40.7168,
      longitude: -74.01,
      timestamp: new Date(new Date().getTime() - 25 * 60000).toISOString(), // 25 minutes ago
    },
    workLocation: {
      name: "Hospital Site",
    },
    distance: 0.8, // km from work location
    type: "OUTSIDE_AREA",
  },
]);

const workLocations = ref([
  {
    id: "1",
    name: "Office Building Site",
    address: "123 Main St, New York, NY",
    latitude: 40.7128,
    longitude: -74.006,
    radius: 500, // meters
    isDefault: true,
    isActive: true,
  },
  {
    id: "2",
    name: "Residential Complex Site",
    address: "456 Park Ave, New York, NY",
    latitude: 40.7138,
    longitude: -74.007,
    radius: 300, // meters
    isDefault: false,
    isActive: true,
  },
  {
    id: "3",
    name: "Highway Bridge Site",
    address: "789 Broadway, New York, NY",
    latitude: 40.7148,
    longitude: -74.008,
    radius: 1000, // meters
    isDefault: false,
    isActive: true,
  },
  {
    id: "4",
    name: "Shopping Mall Site",
    address: "101 5th Ave, New York, NY",
    latitude: 40.7158,
    longitude: -74.009,
    radius: 400, // meters
    isDefault: false,
    isActive: true,
  },
  {
    id: "5",
    name: "Hospital Site",
    address: "202 Madison Ave, New York, NY",
    latitude: 40.7168,
    longitude: -74.01,
    radius: 600, // meters
    isDefault: false,
    isActive: true,
  },
]);

// Computed properties
const mapLocations = computed(() => {
  const locations = [];

  // Add worker locations
  if (selectedView.value === "live" || selectedView.value === "history") {
    activeWorkers.value.forEach((worker) => {
      if (
        selectedLocation.value === "all" ||
        worker.project === selectedLocation.value
      ) {
        locations.push({
          id: `worker-${worker.id}`,
          latitude: worker.location.latitude,
          longitude: worker.location.longitude,
          name: worker.name,
          type: "worker",
          worker: {
            id: worker.id,
            name: worker.name,
            avatar: worker.avatar,
          },
          project: {
            name: worker.project,
          },
          status: worker.isCompliant ? "compliant" : "non-compliant",
        });
      }
    });
  }

  // Add alert locations
  if (selectedView.value === "alerts") {
    locationAlerts.value.forEach((alert) => {
      if (
        selectedLocation.value === "all" ||
        alert.project === selectedLocation.value
      ) {
        locations.push({
          id: `alert-${alert.id}`,
          latitude: alert.location.latitude,
          longitude: alert.location.longitude,
          name: `${alert.worker.name} - ${
            alert.type === "OUTSIDE_AREA" ? "Outside Work Area" : "Alert"
          }`,
          type: "alert",
          worker: {
            id: alert.worker.id,
            name: alert.worker.name,
            avatar: alert.worker.avatar,
          },
          project: {
            name: alert.project,
          },
          status: "alert",
        });
      }
    });
  }

  // Add work locations
  workLocations.value.forEach((location) => {
    if (
      selectedLocation.value === "all" ||
      selectedLocation.value === location.id
    ) {
      locations.push({
        id: `location-${location.id}`,
        latitude: location.latitude,
        longitude: location.longitude,
        name: location.name,
        address: location.address,
        type: "project",
        radius: location.radius,
      });
    }
  });

  return locations;
});

const mapCenter = computed(() => {
  // If a specific location is selected, center on that
  if (selectedLocation.value !== "all") {
    const location = workLocations.value.find(
      (loc) => loc.id === selectedLocation.value
    );
    if (location) {
      return [location.longitude, location.latitude];
    }
  }

  // If a worker is selected, center on that worker
  if (selectedWorkerId.value) {
    const worker = activeWorkers.value.find(
      (w) => w.id === selectedWorkerId.value
    );
    if (worker) {
      return [worker.location.longitude, worker.location.latitude];
    }
  }

  // Default to Tallinn, Estonia
  return [24.7536, 59.437];
});

// Methods
const getViewTitle = () => {
  switch (selectedView.value) {
    case "live":
      return "Live Worker Locations";
    case "history":
      return "Location History";
    case "alerts":
      return "Compliance Alerts Map";
    default:
      return "Location Map";
  }
};

const refreshMap = () => {
  isMapLoading.value = true;

  // Simulate API call
  setTimeout(() => {
    isMapLoading.value = false;

    // Refresh the map markers
    if (mapRef.value) {
      mapRef.value.addMarkers();
    }
  }, 1000);
};

const focusWorkerOnMap = (worker) => {
  selectedWorkerId.value = worker.id;

  // Focus the map on the worker's location
  if (mapRef.value) {
    const workerLocation = {
      id: `worker-${worker.id}`,
      latitude: worker.location.latitude,
      longitude: worker.location.longitude,
    };
    mapRef.value.flyToLocation(workerLocation);
  }
};

const selectWorkLocation = (location) => {
  selectedLocation.value = location.id;

  // Focus the map on the work location
  if (mapRef.value) {
    const mapLocation = {
      id: `location-${location.id}`,
      latitude: location.latitude,
      longitude: location.longitude,
    };
    mapRef.value.flyToLocation(mapLocation);
  }
};

const handleMarkerClick = (location) => {
  // If it's a worker location, select that worker
  if (location.id.startsWith("worker-")) {
    const workerId = location.id.replace("worker-", "");
    selectedWorkerId.value = workerId;

    // Highlight the worker in the list
    const workerElement = document.querySelector(
      `[data-worker-id="${workerId}"]`
    );
    if (workerElement) {
      workerElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }

  // If it's a work location, select that location
  if (location.id.startsWith("location-")) {
    const locationId = location.id.replace("location-", "");
    selectedLocation.value = locationId;

    // Highlight the location in the list
    const locationElement = document.querySelector(
      `[data-location-id="${locationId}"]`
    );
    if (locationElement) {
      locationElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }
};

// Lifecycle hooks
onMounted(() => {
  // Simulate API calls
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);

  setTimeout(() => {
    isMapLoading.value = false;
  }, 1500);
});
</script>

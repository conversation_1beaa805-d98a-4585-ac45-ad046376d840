{"lastValidatedTimestamp": *************, "projects": {"C:\\Users\\<USER>\\comanager\\client": {"name": "tairo", "version": "2.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\.app": {"name": "app"}, "C:\\Users\\<USER>\\comanager\\client\\.demo": {"name": "demo", "version": "0.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\.landing": {"name": "demo", "version": "0.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\accounting": {"name": "@comanager/accounting-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\budget": {"name": "budget-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\communication": {"name": "@comanager/communication-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\companies": {"name": "@comanager/companies-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\core": {"name": "core-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\hr": {"name": "@comanager/hr-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\production": {"name": "@comanager/production-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\recruitment": {"name": "@comanager/recruitment-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\sales": {"name": "@comanager/sales-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\tairo": {"name": "@cssninja/tairo", "version": "0.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\layers\\timemanagement": {"name": "@comanager/timemanagement-layer", "version": "1.0.0"}, "C:\\Users\\<USER>\\comanager\\client\\tairo-component-meta": {"name": "@cssninja/tairo-component-meta", "version": "0.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": [".demo", ".app", ".landing", "layers/*", "tairo-component-meta"]}, "filteredInstall": true}
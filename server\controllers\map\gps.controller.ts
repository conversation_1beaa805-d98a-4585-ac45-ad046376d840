// server/controllers/map/gps.controller.ts

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { AuthenticatedRequest } from "../../middleware/auth";

const prisma = new PrismaClient();

// Create or update GPS device
export const createGpsDevice = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const {
      deviceId,
      name,
      description,
      assetId,
      reportingInterval,
      geofenceRadius,
    } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user's company
    const userCompany = await prisma.userCompany.findFirst({
      where: { userId },
      include: { company: true },
    });

    if (!userCompany) {
      res.status(403).json({ error: "User not associated with any company" });
      return;
    }

    const gpsDevice = await prisma.gpsDevice.create({
      data: {
        deviceId,
        name,
        description,
        assetId: assetId ? parseInt(assetId) : null,
        reportingInterval: reportingInterval || 300,
        geofenceRadius: geofenceRadius ? parseFloat(geofenceRadius) : null,
        companyId: userCompany.companyId,
      },
      include: {
        asset: true,
        company: true,
      },
    });

    res.status(201).json(gpsDevice);
  } catch (error) {
    console.error("Error creating GPS device:", error);
    res.status(500).json({ error: "Failed to create GPS device" });
  }
};

// Receive GPS location data from device
export const receiveGpsLocation = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      deviceId,
      latitude,
      longitude,
      altitude,
      accuracy,
      speed,
      heading,
      timestamp,
      batteryLevel,
      signalStrength,
    } = req.body;

    // Find GPS device
    const device = await prisma.gpsDevice.findUnique({
      where: { deviceId },
      include: { asset: true },
    });

    if (!device) {
      res.status(404).json({ error: "GPS device not found" });
      return;
    }

    // Create location record
    const location = await prisma.gpsLocation.create({
      data: {
        deviceId: device.id,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        altitude: altitude ? parseFloat(altitude) : null,
        accuracy: accuracy ? parseFloat(accuracy) : null,
        speed: speed ? parseFloat(speed) : null,
        heading: heading ? parseFloat(heading) : null,
        timestamp: timestamp ? new Date(timestamp) : new Date(),
        batteryLevel: batteryLevel ? parseFloat(batteryLevel) : null,
        signalStrength: signalStrength ? parseFloat(signalStrength) : null,
      },
    });

    // Update device last seen and battery level
    await prisma.gpsDevice.update({
      where: { id: device.id },
      data: {
        lastSeen: new Date(),
        batteryLevel: batteryLevel
          ? parseFloat(batteryLevel)
          : device.batteryLevel,
      },
    });

    // Update asset location if device is attached to an asset
    if (device.assetId) {
      await prisma.trackableAssetLocation.upsert({
        where: { assetId: device.assetId },
        create: {
          assetId: device.assetId,
          latitude: parseFloat(latitude),
          longitude: parseFloat(longitude),
          lastUpdate: new Date(),
          isInGeofence: true, // TODO: Calculate geofence status
        },
        update: {
          latitude: parseFloat(latitude),
          longitude: parseFloat(longitude),
          lastUpdate: new Date(),
          // TODO: Update geofence status
        },
      });
    }

    res.status(201).json({ success: true, locationId: location.id });
  } catch (error) {
    console.error("Error receiving GPS location:", error);
    res.status(500).json({ error: "Failed to process GPS location" });
  }
};

// Get GPS devices for a company
export const getGpsDevices = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user's companies
    const userCompanies = await prisma.userCompany.findMany({
      where: { userId },
    });

    const companyIds = userCompanies.map((uc) => uc.companyId);

    const devices = await prisma.gpsDevice.findMany({
      where: {
        companyId: { in: companyIds },
      },
      include: {
        asset: true,
        company: true,
        locationHistory: {
          orderBy: { timestamp: "desc" },
          take: 1,
        },
      },
    });

    res.json(devices);
  } catch (error) {
    console.error("Error fetching GPS devices:", error);
    res.status(500).json({ error: "Failed to fetch GPS devices" });
  }
};

// Get location history for a device
export const getLocationHistory = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { startDate, endDate, limit = 100 } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Check if user has access to this device
    const device = await prisma.gpsDevice.findUnique({
      where: { deviceId },
      include: { company: { include: { UserCompany: true } } },
    });

    if (!device) {
      res.status(404).json({ error: "GPS device not found" });
      return;
    }

    const hasAccess = device.company.UserCompany.some(
      (uc) => uc.userId === userId
    );
    if (!hasAccess) {
      res.status(403).json({ error: "Access denied" });
      return;
    }

    // Build date filter
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = new Date(startDate as string);
    }
    if (endDate) {
      dateFilter.lte = new Date(endDate as string);
    }

    const locations = await prisma.gpsLocation.findMany({
      where: {
        deviceId: device.id,
        ...(Object.keys(dateFilter).length > 0 && { timestamp: dateFilter }),
      },
      orderBy: { timestamp: "desc" },
      take: parseInt(limit as string),
    });

    res.json(locations);
  } catch (error) {
    console.error("Error fetching location history:", error);
    res.status(500).json({ error: "Failed to fetch location history" });
  }
};

// Update worker location (for mobile app)
export const updateWorkerLocation = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const { latitude, longitude, accuracy, projectId, taskId, timeEntryId } =
      req.body;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Check if location is within allowed work locations
    let isCompliant = true;
    let workLocationId = null;

    if (projectId) {
      const workLocations = await prisma.workLocation.findMany({
        where: {
          company: {
            projects: { some: { id: parseInt(projectId) } },
          },
          isActive: true,
        },
      });

      // Check if worker is within any work location radius
      for (const workLocation of workLocations) {
        const distance = calculateDistance(
          parseFloat(latitude),
          parseFloat(longitude),
          workLocation.latitude,
          workLocation.longitude
        );

        if (distance <= workLocation.radius) {
          isCompliant = true;
          workLocationId = workLocation.id;
          break;
        } else {
          isCompliant = false;
        }
      }
    }

    const workerLocation = await prisma.workerLocation.create({
      data: {
        workerId: userId,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        accuracy: accuracy ? parseFloat(accuracy) : null,
        timestamp: new Date(),
        projectId: projectId ? parseInt(projectId) : null,
        taskId: taskId ? parseInt(taskId) : null,
        timeEntryId: timeEntryId ? parseInt(timeEntryId) : null,
        isCompliant,
        workLocationId,
      },
    });

    res.status(201).json({
      success: true,
      locationId: workerLocation.id,
      isCompliant,
    });
  } catch (error) {
    console.error("Error updating worker location:", error);
    res.status(500).json({ error: "Failed to update worker location" });
  }
};

// Helper function to calculate distance between two points
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

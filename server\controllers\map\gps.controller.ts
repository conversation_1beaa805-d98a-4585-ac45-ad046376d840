// server/controllers/map/gps.controller.ts

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Mock data store for GPS devices (replace with database when ready)
const mockGpsDevices: any[] = [];
const mockGpsLocations: any[] = [];

// Create or update GPS device
export const createGpsDevice = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      deviceId,
      name,
      description,
      assetId,
      reportingInterval,
      geofenceRadius,
    } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user's company
    const userCompany = await prisma.userCompany.findFirst({
      where: { userId },
      include: { company: true },
    });

    if (!userCompany) {
      res.status(403).json({ error: "User not associated with any company" });
      return;
    }

    // TODO: Implement GPS device creation when schema is ready
    const mockGpsDevice = {
      id: Math.floor(Math.random() * 1000),
      deviceId,
      name,
      description,
      assetId: assetId ? parseInt(assetId) : null,
      reportingInterval: reportingInterval || 300,
      geofenceRadius: geofenceRadius ? parseFloat(geofenceRadius) : null,
      companyId: userCompany.companyId,
      isActive: true,
      createdAt: new Date(),
    };

    mockGpsDevices.push(mockGpsDevice);
    res.status(201).json(mockGpsDevice);
  } catch (error) {
    console.error("Error creating GPS device:", error);
    res.status(500).json({ error: "Failed to create GPS device" });
  }
};

// Receive GPS location data from device
export const receiveGpsLocation = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const {
      deviceId,
      latitude,
      longitude,
      altitude,
      accuracy,
      speed,
      heading,
      timestamp,
      batteryLevel,
      signalStrength,
    } = req.body;

    // TODO: Implement GPS location processing when schema is ready
    const mockLocation = {
      id: Math.floor(Math.random() * 1000),
      deviceId,
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      altitude: altitude ? parseFloat(altitude) : null,
      accuracy: accuracy ? parseFloat(accuracy) : null,
      speed: speed ? parseFloat(speed) : null,
      heading: heading ? parseFloat(heading) : null,
      timestamp: timestamp ? new Date(timestamp) : new Date(),
      batteryLevel: batteryLevel ? parseFloat(batteryLevel) : null,
      signalStrength: signalStrength ? parseFloat(signalStrength) : null,
    };

    mockGpsLocations.push(mockLocation);
    console.log("Received GPS location:", mockLocation);

    res.status(201).json({ success: true, locationId: mockLocation.id });
  } catch (error) {
    console.error("Error receiving GPS location:", error);
    res.status(500).json({ error: "Failed to process GPS location" });
  }
};

// Get GPS devices for a company
export const getGpsDevices = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Return mock GPS devices for now
    res.json(mockGpsDevices);
  } catch (error) {
    console.error("Error fetching GPS devices:", error);
    res.status(500).json({ error: "Failed to fetch GPS devices" });
  }
};

// Get location history for a device
export const getLocationHistory = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { startDate, endDate, limit = 100 } = req.query;
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Return mock location history for the device
    const deviceLocations = mockGpsLocations
      .filter((loc) => loc.deviceId === deviceId)
      .slice(0, parseInt(limit as string));

    res.json(deviceLocations);
  } catch (error) {
    console.error("Error fetching location history:", error);
    res.status(500).json({ error: "Failed to fetch location history" });
  }
};

// Update worker location (for mobile app)
export const updateWorkerLocation = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { latitude, longitude, accuracy, projectId, taskId, timeEntryId } =
      req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // TODO: Implement worker location tracking when schema is ready
    const mockWorkerLocation = {
      id: Math.floor(Math.random() * 1000),
      workerId: userId,
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      accuracy: accuracy ? parseFloat(accuracy) : null,
      timestamp: new Date(),
      projectId: projectId ? parseInt(projectId) : null,
      taskId: taskId ? parseInt(taskId) : null,
      timeEntryId: timeEntryId ? parseInt(timeEntryId) : null,
      isCompliant: true, // Default to compliant for now
    };

    console.log("Worker location updated:", mockWorkerLocation);

    res.status(201).json({
      success: true,
      locationId: mockWorkerLocation.id,
      isCompliant: mockWorkerLocation.isCompliant,
    });
  } catch (error) {
    console.error("Error updating worker location:", error);
    res.status(500).json({ error: "Failed to update worker location" });
  }
};

// Helper function to calculate distance between two points
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

// server/controllers/map.controller.ts

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { AuthenticatedRequest } from "../../middleware/auth";

const prisma = new PrismaClient();

// Get map data with role-based filtering
export const getMapData = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const { layers, bounds } = req.query;

    if (!userId) {
      res.status(401).json({ error: "Unauthorized" });
      return;
    }

    // Get user roles and company
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: true,
        companies: {
          include: {
            company: true,
          },
        },
      },
    });

    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }

    const userRoles = user.userRoles.map((ur) => ur.role);
    const companyIds = user.companies.map((uc) => uc.companyId);

    // Parse requested layers
    const requestedLayers = layers ? (layers as string).split(",") : ["all"];

    const mapData: any = {
      type: "FeatureCollection",
      features: [],
    };

    // Get projects if requested
    if (
      requestedLayers.includes("projects") ||
      requestedLayers.includes("all")
    ) {
      const projects = await getProjectsForMap(userRoles, companyIds, userId);
      mapData.features.push(...projects);
    }

    // Get workers if requested
    if (
      requestedLayers.includes("workers") ||
      requestedLayers.includes("all")
    ) {
      const workers = await getWorkersForMap(userRoles, companyIds, userId);
      mapData.features.push(...workers);
    }

    // Get assets if requested
    if (requestedLayers.includes("assets") || requestedLayers.includes("all")) {
      const assets = await getAssetsForMap(userRoles, companyIds, userId);
      mapData.features.push(...assets);
    }

    // Get work locations if requested
    if (
      requestedLayers.includes("work_locations") ||
      requestedLayers.includes("all")
    ) {
      const workLocations = await getWorkLocationsForMap(userRoles, companyIds);
      mapData.features.push(...workLocations);
    }

    res.json(mapData);
  } catch (error) {
    console.error("Error fetching map data:", error);
    res.status(500).json({ error: "Failed to fetch map data" });
  }
};

// Get projects based on user role
async function getProjectsForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let projectFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all projects in their companies
    projectFilter = { companyId: { in: companyIds } };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see their assigned projects
    projectFilter = {
      OR: [{ projectLeadId: userId }, { companyId: { in: companyIds } }],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see projects they're involved in
    projectFilter = { clientId: { in: companyIds } };
  } else {
    // Workers see projects they're assigned to
    projectFilter = {
      OR: [
        { assignedWorkers: { some: { id: userId } } },
        { timeEntries: { some: { userId } } },
      ],
    };
  }

  const projects = await prisma.project.findMany({
    where: projectFilter,
    include: {
      company: true,
      client: true,
      workLocations: true,
    },
  });

  return projects
    .filter((project) => project.workLocations.length > 0)
    .map((project) => ({
      type: "Feature",
      id: `project-${project.id}`,
      properties: {
        id: project.id,
        name: project.name,
        description: project.description,
        status: project.status,
        type: "project",
        company: project.company.name,
        client: project.client.name,
        startDate: project.startDate,
        endDate: project.endDate,
        budget: project.budget,
      },
      geometry: {
        type: "Point",
        coordinates: [
          project.workLocations[0].longitude,
          project.workLocations[0].latitude,
        ],
      },
    }));
}

// Get worker locations based on user role
async function getWorkersForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let workerFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all workers in their companies
    workerFilter = {
      companies: { some: { companyId: { in: companyIds } } },
    };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see workers on their projects
    workerFilter = {
      OR: [
        { assignedProjects: { some: { projectLeadId: userId } } },
        { companies: { some: { companyId: { in: companyIds } } } },
      ],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see workers on their projects
    workerFilter = {
      assignedProjects: { some: { clientId: { in: companyIds } } },
    };
  } else {
    // Workers see only themselves
    workerFilter = { id: userId };
  }

  // Get recent worker locations (last 24 hours)
  const recentLocations = await prisma.workerLocation.findMany({
    where: {
      worker: workerFilter,
      timestamp: {
        gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
      },
    },
    include: {
      worker: {
        include: {
          companies: {
            include: { company: true },
          },
        },
      },
      project: true,
      task: true,
      timeEntry: true,
    },
    orderBy: {
      timestamp: "desc",
    },
  });

  // Group by worker and get latest location
  const workerLocationMap = new Map();
  recentLocations.forEach((location) => {
    if (!workerLocationMap.has(location.workerId)) {
      workerLocationMap.set(location.workerId, location);
    }
  });

  return Array.from(workerLocationMap.values()).map((location: any) => ({
    type: "Feature",
    id: `worker-${location.workerId}`,
    properties: {
      id: location.workerId,
      name: `${location.worker.firstName} ${location.worker.lastName}`,
      type: "worker",
      status: location.isCompliant ? "compliant" : "non-compliant",
      project: location.project?.name,
      task: location.task?.name,
      timestamp: location.timestamp,
      accuracy: location.accuracy,
      company: location.worker.companies[0]?.company?.name,
    },
    geometry: {
      type: "Point",
      coordinates: [location.longitude, location.latitude],
    },
  }));
}

// Get trackable assets based on user role
async function getAssetsForMap(
  userRoles: string[],
  companyIds: number[],
  userId: number
) {
  let assetFilter: any = {};

  if (userRoles.includes("SUPERADMIN") || userRoles.includes("ADMIN")) {
    // Admins can see all assets in their companies
    assetFilter = { companyId: { in: companyIds } };
  } else if (userRoles.includes("PROJECTLEADER")) {
    // Project leaders see assets on their projects
    assetFilter = {
      OR: [
        { companyId: { in: companyIds } },
        {
          projectAssignments: { some: { project: { projectLeadId: userId } } },
        },
      ],
    };
  } else if (userRoles.includes("CLIENT")) {
    // Clients see assets on their projects
    assetFilter = {
      projectAssignments: {
        some: { project: { clientId: { in: companyIds } } },
      },
    };
  } else {
    // Workers see assets they're assigned to work with
    assetFilter = {
      projectAssignments: {
        some: {
          project: {
            OR: [
              { assignedWorkers: { some: { id: userId } } },
              { timeEntries: { some: { userId } } },
            ],
          },
        },
      },
    };
  }

  const assets = await prisma.trackableAsset.findMany({
    where: assetFilter,
    include: {
      currentLocation: true,
      company: true,
      projectAssignments: {
        include: { project: true },
        where: { unassignedAt: null },
      },
    },
  });

  return assets
    .filter((asset) => asset.currentLocation)
    .map((asset) => ({
      type: "Feature",
      id: `asset-${asset.id}`,
      properties: {
        id: asset.id,
        name: asset.name,
        description: asset.description,
        type: "asset",
        assetType: asset.type,
        status: asset.status,
        serialNumber: asset.serialNumber,
        model: asset.model,
        manufacturer: asset.manufacturer,
        company: asset.company.name,
        project: asset.projectAssignments[0]?.project?.name,
        lastUpdate: asset.currentLocation?.lastUpdate,
        isInGeofence: asset.currentLocation?.isInGeofence,
      },
      geometry: {
        type: "Point",
        coordinates: [
          asset.currentLocation!.longitude,
          asset.currentLocation!.latitude,
        ],
      },
    }));
}

// Get work locations based on user role
async function getWorkLocationsForMap(
  userRoles: string[],
  companyIds: number[]
) {
  const workLocations = await prisma.workLocation.findMany({
    where: {
      companyId: { in: companyIds },
      isActive: true,
    },
    include: {
      company: true,
    },
  });

  return workLocations.map((location) => ({
    type: "Feature",
    id: `work-location-${location.id}`,
    properties: {
      id: location.id,
      name: location.name,
      description: location.description,
      type: "work_location",
      address: location.address,
      radius: location.radius,
      isDefault: location.isDefault,
      company: location.company.name,
    },
    geometry: {
      type: "Point",
      coordinates: [location.longitude, location.latitude],
    },
  }));
}

// server/routes/map.routes.ts

import express from "express";
import { authenticateToken } from "../middleware/auth";
import { getMapData } from "../controllers/map/map.controller";
import {
  createGpsDevice,
  receiveGpsLocation,
  getGpsDevices,
  getLocationHistory,
  updateWorkerLocation,
} from "../controllers/map/gps.controller";

const router = express.Router();

// Map data routes
router.get("/data", authenticateToken, getMapData);

// GPS device routes
router.post("/gps/devices", authenticateToken, createGpsDevice);
router.get("/gps/devices", authenticateToken, getGpsDevices);
router.post("/gps/location", receiveGpsLocation); // No auth for GPS devices
router.get(
  "/gps/devices/:deviceId/history",
  authenticateToken,
  getLocationHistory
);

// Worker location routes
router.post("/worker/location", authenticateToken, updateWorkerLocation);

export default router;

// server/utils/route-helpers.ts

import { Request, Response, NextFunction, RequestHandler } from "express";
import { authenticateToken as authToken } from "../middleware/authenticateToken.js";

/**
 * A type-safe wrapper for the authenticateToken middleware
 * This resolves the TypeScript errors with Express type definitions
 */
export const auth = authToken as RequestHandler;

/**
 * A type-safe wrapper for any middleware function
 * Use this for any middleware that's causing TypeScript errors
 */
export function wrapHandler(handler: any): RequestHandler {
  return handler as <PERSON>quest<PERSON>and<PERSON>;
}

/**
 * A type-safe wrapper for controller functions
 * Use this for any controller function that's causing TypeScript errors
 */
export function wrapController(controller: any): RequestHandler {
  return controller as <PERSON><PERSON><PERSON>and<PERSON>;
}

/**
 * A utility function to create a route handler with proper typing
 */
export function createHandler(
  handler: (
    req: Request,
    res: Response,
    next: NextFunction
  ) => Promise<any> | any
): RequestHandler {
  return handler as <PERSON><PERSON><PERSON>and<PERSON>;
}
